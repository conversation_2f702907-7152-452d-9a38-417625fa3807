/**
 * 确认项模板 - 基于AI辅助质量体系文档标准
 * 遵循可操作性、可见性、客观性、时效性原则
 */
import fs from 'fs';

/**
 * 生成基础任务确认清单
 */
export function generateBasicChecklist(taskData) {
  const checklist = {
    taskId: taskData.id,
    taskName: taskData.name,
    type: 'basic',
    estimatedTime: '3-5分钟',
    generatedAt: new Date().toISOString(),
    items: [],
  };

  // 文件存在性检查
  if (taskData.relatedFiles && taskData.relatedFiles.length > 0) {
    taskData.relatedFiles.forEach((file) => {
      if (file.type === 'CREATE' || file.type === 'TO_MODIFY') {
        checklist.items.push({
          id: `file_${file.path.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: 'file_check',
          description: `能看到 ${file.path} 文件已创建`,
          instruction: `在VS Code或文件管理器中确认 ${file.path} 文件存在`,
          filePath: file.path,
          required: true,
          completed: false,
        });
      }
    });
  }

  // 命令执行检查
  const commonCommands = [
    { cmd: 'pnpm type-check', expected: '无TypeScript错误' },
    { cmd: 'pnpm lint:check', expected: '无ESLint错误' },
    { cmd: 'pnpm format:check', expected: '代码格式正确' },
  ];

  commonCommands.forEach((cmdInfo) => {
    checklist.items.push({
      id: `cmd_${cmdInfo.cmd.replace(/[^a-zA-Z0-9]/g, '_')}`,
      type: 'command_check',
      description: `运行 ${cmdInfo.cmd} 显示 ${cmdInfo.expected}`,
      instruction: `在终端中执行 ${cmdInfo.cmd} 并确认输出无错误`,
      command: cmdInfo.cmd,
      expectedResult: cmdInfo.expected,
      required: true,
      completed: false,
    });
  });

  return checklist;
}

/**
 * 生成功能任务确认清单
 */
export function generateFeatureChecklist(taskData) {
  const checklist = {
    taskId: taskData.id,
    taskName: taskData.name,
    type: 'feature',
    estimatedTime: '5-10分钟',
    generatedAt: new Date().toISOString(),
    items: [],
  };

  // 浏览器验证检查
  checklist.items.push({
    id: 'browser_basic',
    type: 'browser_check',
    description: '访问 http://localhost:3000 能正常显示页面',
    instruction: '在浏览器中访问 http://localhost:3000 并确认页面正常加载',
    url: 'http://localhost:3000',
    expectedContent: '页面正常显示',
    required: true,
    completed: false,
  });

  // 功能交互检查
  checklist.items.push({
    id: 'interaction_basic',
    type: 'interaction_check',
    description: '页面交互功能正常工作',
    instruction: '测试页面中的按钮、链接等交互元素',
    element: '交互元素',
    expectedReaction: '正常响应',
    required: true,
    completed: false,
  });

  // 响应式检查
  checklist.items.push({
    id: 'responsive_basic',
    type: 'responsive_check',
    description: '在不同屏幕尺寸下布局正常',
    instruction: '调整浏览器窗口大小，确认响应式布局正常',
    required: false,
    completed: false,
  });

  return checklist;
}

/**
 * 生成集成任务确认清单
 */
export function generateIntegrationChecklist(taskData) {
  const checklist = {
    taskId: taskData.id,
    taskName: taskData.name,
    type: 'integration',
    estimatedTime: '7-10分钟',
    generatedAt: new Date().toISOString(),
    items: [],
  };

  // 构建验证检查
  checklist.items.push({
    id: 'build_verification',
    type: 'build_check',
    description: '运行 pnpm build 构建成功',
    instruction: '在终端中执行 pnpm build 并确认构建无错误',
    command: 'pnpm build',
    expectedResult: '构建成功',
    required: true,
    completed: false,
  });

  // 服务启动检查
  checklist.items.push({
    id: 'service_start',
    type: 'service_check',
    description: '运行 pnpm dev 服务正常启动',
    instruction: '在终端中执行 pnpm dev 并确认开发服务器启动',
    command: 'pnpm dev',
    expectedResult: '服务器在 http://localhost:3000 启动',
    required: true,
    completed: false,
  });

  // 错误处理检查
  checklist.items.push({
    id: 'error_handling',
    type: 'error_check',
    description: '错误情况能正确处理',
    instruction: '测试错误场景（如404页面）的处理',
    errorCase: '404错误',
    expectedHandling: '显示友好的错误页面',
    required: false,
    completed: false,
  });

  return checklist;
}

/**
 * 根据任务类型自动选择合适的确认清单模板
 */
export function generateChecklistByTaskType(taskData) {
  const taskName = taskData.name.toLowerCase();

  if (
    taskName.includes('集成') ||
    taskName.includes('配置') ||
    taskName.includes('系统')
  ) {
    return generateIntegrationChecklist(taskData);
  } else if (
    taskName.includes('组件') ||
    taskName.includes('页面') ||
    taskName.includes('功能')
  ) {
    return generateFeatureChecklist(taskData);
  } else {
    return generateBasicChecklist(taskData);
  }
}

/**
 * 验证确认清单的完整性
 */
export function validateChecklist(checklist) {
  const validation = {
    isValid: true,
    errors: [],
    warnings: [],
  };

  // 检查必需字段
  if (!checklist.taskId) {
    validation.errors.push('缺少任务ID');
    validation.isValid = false;
  }

  if (!checklist.items || checklist.items.length === 0) {
    validation.errors.push('确认清单为空');
    validation.isValid = false;
  }

  // 检查时效性原则（3-10分钟）
  const estimatedMinutes = parseInt(checklist.estimatedTime);
  if (estimatedMinutes < 3 || estimatedMinutes > 10) {
    validation.warnings.push('预估时间超出3-10分钟范围');
  }

  // 检查可操作性原则
  checklist.items.forEach((item, index) => {
    if (!item.instruction || item.instruction.length < 10) {
      validation.warnings.push(`第${index + 1}项缺少详细操作指导`);
    }
  });

  return validation;
}

/**
 * 保存确认清单到文件
 */
export function saveChecklistToFile(checklist, outputPath) {
  try {
    const validation = validateChecklist(checklist);
    if (!validation.isValid) {
      throw new Error(`确认清单验证失败: ${validation.errors.join(', ')}`);
    }

    fs.writeFileSync(outputPath, JSON.stringify(checklist, null, 2));
    return {
      success: true,
      path: outputPath,
      validation,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复解析错误的脚本
 */

function fixParsingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复导入语句格式问题
    const importPatterns = [
      // 修复缺少 import 关键字的情况
      {
        pattern: /^\s*type\s+(\w+),\s*(\w+)\s*}\s*from\s*['"]([^'"]+)['"];/gm,
        replacement: 'import { type $1, $2 } from \'$3\';'
      },
      
      // 修复导入语句中的格式问题
      {
        pattern: /^\s*(\w+)\s*}\s*from\s*['"]([^'"]+)['"];/gm,
        replacement: 'import { $1 } from \'$2\';'
      }
    ];
    
    // 2. 修复函数声明格式问题
    const functionPatterns = [
      // 修复函数参数格式
      {
        pattern: /function\s+(\w+)\s*\(\s*([^)]*)\s*\)\s*:\s*([^{]+)\s*\{/g,
        replacement: 'function $1($2): $3 {'
      }
    ];
    
    // 3. 修复变量声明格式问题
    const variablePatterns = [
      // 修复常量声明
      {
        pattern: /const\s+(\w+)\s*=\s*([^;]+);\s*([^;]+);/g,
        replacement: 'const $1 = $2;\n$3;'
      }
    ];
    
    // 4. 修复对象和数组格式问题
    const objectPatterns = [
      // 修复对象属性格式
      {
        pattern: /\{\s*([^}]+)\s*\}\s*:\s*\{/g,
        replacement: (match, content) => {
          const cleanContent = content.replace(/\s+/g, ' ').trim();
          return `{\n  ${cleanContent}\n}: {`;
        }
      }
    ];
    
    // 5. 修复条件表达式格式问题
    const conditionalPatterns = [
      // 修复三元运算符格式
      {
        pattern: /\?\s*([^:]+)\s*:\s*([^;]+);/g,
        replacement: '? $1 : $2;'
      },
      
      // 修复逻辑运算符格式
      {
        pattern: /&&\s*([^&]+)&&/g,
        replacement: '&& $1 &&'
      }
    ];
    
    // 应用所有修复模式
    const allPatterns = [
      ...importPatterns,
      ...functionPatterns,
      ...variablePatterns,
      ...objectPatterns,
      ...conditionalPatterns
    ];
    
    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 6. 修复行内多语句问题
    const lines = content.split('\n');
    let linesModified = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      
      // 检查是否有多个语句在一行（简单检测）
      if (trimmed.includes(';') && trimmed.includes('{') && trimmed.includes('}')) {
        // 尝试分离语句
        const statements = trimmed.split(';').filter(s => s.trim());
        if (statements.length > 1) {
          const indent = line.match(/^\s*/)[0];
          const newLines = statements.map((stmt, idx) => {
            const cleanStmt = stmt.trim();
            if (idx === statements.length - 1 && !cleanStmt.endsWith(';')) {
              return indent + cleanStmt;
            }
            return indent + cleanStmt + (cleanStmt ? ';' : '');
          }).filter(l => l.trim());
          
          if (newLines.length > 1) {
            lines.splice(i, 1, ...newLines);
            linesModified = true;
            i += newLines.length - 1; // 调整索引
          }
        }
      }
    }
    
    if (linesModified) {
      content = lines.join('\n');
      modified = true;
    }
    
    // 7. 修复常见的语法错误
    const syntaxFixes = [
      // 修复缺少分号
      {
        pattern: /(\w+)\s*=\s*([^;]+)(?<!;)\s*$/gm,
        replacement: '$1 = $2;'
      },
      
      // 修复缺少逗号
      {
        pattern: /(\w+):\s*([^,}]+)\s+(\w+):/g,
        replacement: '$1: $2, $3:'
      }
    ];
    
    for (const { pattern, replacement } of syntaxFixes) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复解析错误: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复解析错误...\n');
  
  // 获取有解析错误的文件
  let errorFiles = [];
  try {
    const lintOutput = execSync('pnpm lint 2>&1 | grep "Parsing error"', { encoding: 'utf8' });
    const lines = lintOutput.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      const match = line.match(/^([^:]+):/);
      if (match) {
        const filePath = match[1].trim();
        if (filePath.startsWith('./')) {
          errorFiles.push(filePath.substring(2));
        } else {
          errorFiles.push(filePath);
        }
      }
    }
  } catch (error) {
    console.log('无法获取解析错误文件列表，将处理所有文件');
  }
  
  // 去重
  errorFiles = [...new Set(errorFiles)];
  
  if (errorFiles.length === 0) {
    console.log('未找到有解析错误的文件，处理所有 TypeScript 文件');
    // 如果没有找到特定文件，处理所有文件
    const extensions = ['.ts', '.tsx'];
    const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
    
    for (const dir of directories) {
      if (!fs.existsSync(dir)) continue;
      
      function processDirectory(dirPath) {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const fullPath = path.join(dirPath, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            processDirectory(fullPath);
          } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
            errorFiles.push(fullPath);
          }
        }
      }
      
      processDirectory(dir);
    }
  }
  
  let totalFixed = 0;
  
  for (const filePath of errorFiles) {
    if (fs.existsSync(filePath) && fixParsingErrors(filePath)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的解析错误`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep -c "Parsing error"', { encoding: 'utf8' });
    console.log(`剩余解析错误: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixParsingErrors };

#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复 TestWrapper 和其他格式化问题的脚本
 */

function fixTestWrapper(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 检查是否有格式化问题（多个语句在一行）
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      
      // 检查是否有多个语句在一行
      if (trimmed.includes('const ') && trimmed.includes('= (') && trimmed.includes('=> {') && trimmed.includes('};')) {
        console.log(`⚠️  检测到格式化问题: ${filePath}:${i + 1}`);
        
        // 尝试修复这一行
        let fixed = trimmed;
        
        // 分离注释
        const commentMatch = fixed.match(/^(\/\/[^;]*?)\s*(const.*)/);
        if (commentMatch) {
          const comment = commentMatch[1];
          const code = commentMatch[2];
          
          // 格式化代码部分
          let formattedCode = code;
          
          // 在 const 和 = 之间添加换行
          formattedCode = formattedCode.replace(/const\s+(\w+)\s*=\s*\(\{/, 'const $1 = ({\n  ');
          
          // 在参数之间添加换行
          formattedCode = formattedCode.replace(/,\s*\}\s*:\s*\{/, ',\n}: {');
          
          // 在类型定义之间添加换行
          formattedCode = formattedCode.replace(/;\s*\}\)\s*:\s*(\w+)\s*=>\s*\{/, ';\n}): $1 => {');
          
          // 在函数体内添加换行
          formattedCode = formattedCode.replace(/=>\s*\{\s*const/, '=> {\n  const');
          formattedCode = formattedCode.replace(/\}\)\s*;\s*return/, '});\n  return');
          formattedCode = formattedCode.replace(/return\s*<([^>]+)>\{([^}]+)\}<\/[^>]+>;\s*\};/, 'return <$1>{$2}</$1>;\n};');
          
          lines[i] = comment + '\n' + formattedCode;
          modified = true;
        }
      }
      
      // 修复其他常见的格式化问题
      if (trimmed.includes('const ') && trimmed.includes('= ():') && trimmed.includes('=> {') && trimmed.includes('};')) {
        console.log(`⚠️  检测到函数格式化问题: ${filePath}:${i + 1}`);
        
        let fixed = trimmed;
        
        // 分离注释
        const commentMatch = fixed.match(/^(\/\/[^;]*?)\s*(const.*)/);
        if (commentMatch) {
          const comment = commentMatch[1];
          const code = commentMatch[2];
          
          // 格式化函数
          let formattedCode = code;
          formattedCode = formattedCode.replace(/const\s+(\w+)\s*=\s*\(\)\s*:\s*(\w+)\s*=>\s*\{/, 'const $1 = (): $2 => {');
          formattedCode = formattedCode.replace(/\{\s*render\(/, '{\n  render(');
          formattedCode = formattedCode.replace(/\)\s*;\s*expect/, ');\n  expect');
          formattedCode = formattedCode.replace(/expect\([^)]+\)\.[^;]+;\s*expect/, (match) => {
            return match.replace(/;\s*expect/, ';\n  expect');
          });
          formattedCode = formattedCode.replace(/\)\.[^;]+;\s*\};/, (match) => {
            return match.replace(/;\s*\};/, ';\n};');
          });
          
          lines[i] = comment + '\n' + formattedCode;
          modified = true;
        }
      }
    }
    
    if (modified) {
      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 修复格式化问题: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复 TestWrapper 和格式化问题...\n');
  
  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (fixTestWrapper(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的格式化问题`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep -c "TestWrapper"', { encoding: 'utf8' });
    console.log(`剩余 TestWrapper 错误: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixTestWrapper };

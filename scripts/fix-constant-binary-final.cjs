#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复常量二进制表达式的最终脚本
 */

function fixConstantBinary(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 修复常量二进制表达式
    const patterns = [
      // value !== undefined ?? defaultValue
      {
        pattern: /(\w+)\s*!==\s*undefined\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 ?? $2'
      },
      
      // value !== null ?? defaultValue
      {
        pattern: /(\w+)\s*!==\s*null\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 ?? $2'
      },
      
      // value !== false ?? defaultValue (这个需要特殊处理)
      {
        pattern: /(\w+)\s*!==\s*false\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 !== false ? $1 : $2'
      },
      
      // value !== true ?? defaultValue (这个需要特殊处理)
      {
        pattern: /(\w+)\s*!==\s*true\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 !== true ? $1 : $2'
      },
      
      // value !== 0 ?? defaultValue (这个需要特殊处理)
      {
        pattern: /(\w+)\s*!==\s*0\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 !== 0 ? $1 : $2'
      },
      
      // value !== '' ?? defaultValue (这个需要特殊处理)
      {
        pattern: /(\w+)\s*!==\s*['"]['"]?\s*\?\?\s*([^;,\n)]+)/g,
        replacement: '$1 !== "" ? $1 : $2'
      }
    ];
    
    for (const { pattern, replacement } of patterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复常量二进制表达式: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复常量二进制表达式...\n');
  
  // 获取有常量二进制表达式错误的文件
  let errorFiles = [];
  try {
    const lintOutput = execSync('pnpm lint 2>&1 | grep -B 1 "no-constant-binary-expression"', { encoding: 'utf8' });
    const lines = lintOutput.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.includes('.ts') || line.includes('.tsx')) {
        const match = line.match(/^([^:]+):/);
        if (match) {
          const filePath = match[1].trim();
          if (filePath.startsWith('./')) {
            errorFiles.push(filePath.substring(2));
          } else {
            errorFiles.push(filePath);
          }
        }
      }
    }
  } catch (error) {
    console.log('无法获取常量二进制表达式文件列表');
  }
  
  // 去重
  errorFiles = [...new Set(errorFiles)];
  
  let totalFixed = 0;
  
  for (const filePath of errorFiles) {
    if (fs.existsSync(filePath) && fixConstantBinary(filePath)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的常量二进制表达式`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep "Error:" | wc -l', { encoding: 'utf8' });
    console.log(`剩余错误数量: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixConstantBinary };

#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复格式化问题的脚本
 */

function fixFormatting(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否是单行文件（可能是格式化问题）
    const lines = content.split('\n');
    if (lines.length === 1 && content.length > 200) {
      console.log(`⚠️  检测到可能的格式化问题: ${filePath}`);

      // 尝试在常见的分隔符处添加换行
      let formatted = content;

      // 在 import 语句之间添加换行
      formatted = formatted.replace(/;(\s*import\s)/g, ';\n$1');

      // 在函数声明前添加换行
      formatted = formatted.replace(/;(\s*export\s)/g, ';\n\n$1');
      formatted = formatted.replace(/;(\s*function\s)/g, ';\n\n$1');
      formatted = formatted.replace(/;(\s*const\s+[A-Z])/g, ';\n\n$1');
      formatted = formatted.replace(/;(\s*interface\s)/g, ';\n\n$1');
      formatted = formatted.replace(/;(\s*type\s)/g, ';\n\n$1');

      // 在对象和数组的大括号后添加换行
      formatted = formatted.replace(/{\s*([a-zA-Z])/g, '{\n  $1');
      formatted = formatted.replace(
        /,\s*([a-zA-Z_$][a-zA-Z0-9_$]*:)/g,
        ',\n  $1'
      );
      formatted = formatted.replace(/}\s*;/g, '\n};\n');

      // 在 JSX 元素之间添加换行
      formatted = formatted.replace(/>\s*</g, '>\n      <');
      formatted = formatted.replace(/>\s*{/g, '>\n        {');
      formatted = formatted.replace(/}\s*</g, '}\n      <');

      // 在注释前添加换行
      formatted = formatted.replace(/;(\s*\/\/)/g, ';\n\n  $1');

      if (formatted !== content) {
        fs.writeFileSync(filePath, formatted, 'utf8');
        console.log(`✅ 修复格式化: ${filePath}`);
        modified = true;
      }
    }

    return modified;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复格式化问题...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixFormatting(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的格式化问题`);

  // 运行 Prettier 来进一步格式化
  console.log('\n🎨 运行 Prettier 格式化...');
  try {
    execSync('npx prettier --write "src/**/*.{ts,tsx}"', { stdio: 'inherit' });
    console.log('✅ Prettier 格式化完成');
  } catch {
    console.log('⚠️  Prettier 格式化失败，但继续进行');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFormatting };

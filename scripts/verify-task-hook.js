#!/usr/bin/env node

/**
 * verify_task 后置钩子
 * 在 shrimp-task-manager 的 verify_task 完成后自动触发三层审查
 *
 * 使用方法：
 * node scripts/verify-task-hook.js <taskId> <summary> <originalScore>
 */
import { exec } from 'child_process';
import fs from 'fs';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 日志工具
 */
class Logger {
  static info(message) {
    console.log(`ℹ️  [VERIFY-HOOK] ${message}`);
  }

  static success(message) {
    console.log(`✅ [VERIFY-HOOK] ${message}`);
  }

  static warning(message) {
    console.log(`⚠️  [VERIFY-HOOK] ${message}`);
  }

  static error(message) {
    console.error(`❌ [VERIFY-HOOK] ${message}`);
  }
}

/**
 * 检查是否应该跳过三层审查
 */
function shouldSkipThreeLayerReview(taskId, summary) {
  // 检查是否有紧急跳过标志
  if (process.env.SKIP_THREE_LAYER === 'true') {
    Logger.warning('检测到环境变量 SKIP_THREE_LAYER=true，跳过三层审查');
    return true;
  }

  // 检查任务摘要中是否包含跳过标志
  if (summary.includes('[SKIP_THREE_LAYER]')) {
    Logger.warning('任务摘要包含跳过标志，跳过三层审查');
    return true;
  }

  return false;
}

/**
 * 执行三层审查
 */
async function executeThreeLayerReview(taskId, summary, originalScore) {
  Logger.info('🎯 开始执行三层审查后置钩子');
  Logger.info(`📝 任务ID: ${taskId}`);
  Logger.info(`📋 摘要: ${summary}`);
  Logger.info(`📊 原始评分: ${originalScore}/100`);

  // 检查是否应该跳过
  if (shouldSkipThreeLayerReview(taskId, summary)) {
    Logger.warning('⏭️ 跳过三层审查，使用原始评分');
    return {
      success: true,
      skipped: true,
      finalScore: originalScore,
      taskId,
      summary,
      message: '跳过三层审查，使用原始评分',
    };
  }

  try {
    Logger.info('🚀 启动三层审查包装器...');

    // 调用三层审查包装器
    const command = `node scripts/shrimp-verify-wrapper.js "${taskId}" "${summary}"`;
    Logger.info(`执行命令: ${command}`);

    const { stdout } = await execAsync(command, {
      timeout: 300000, // 5分钟超时
      cwd: process.cwd(),
    });

    // 解析三层审查结果
    const resultMatch = stdout.match(
      /--- THREE_LAYER_RESULT ---([\s\S]*?)--- END_THREE_LAYER_RESULT ---/
    );

    if (resultMatch) {
      const resultJson = resultMatch[1].trim();
      const result = JSON.parse(resultJson);

      Logger.success(`🎉 三层审查完成！最终评分: ${result.finalScore}/100`);

      // 如果三层审查评分高于原始评分，更新评分
      if (result.finalScore > originalScore) {
        Logger.success(`📈 评分提升: ${originalScore} → ${result.finalScore}`);
      } else if (result.finalScore < originalScore) {
        Logger.warning(`📉 评分下降: ${originalScore} → ${result.finalScore}`);
      } else {
        Logger.info(`📊 评分保持: ${result.finalScore}`);
      }

      return {
        ...result,
        originalScore,
        improved: result.finalScore > originalScore,
      };
    } else {
      throw new Error('无法解析三层审查结果');
    }
  } catch (error) {
    Logger.error(`三层审查执行失败: ${error.message}`);

    // 如果三层审查失败，使用原始评分但标记为失败
    return {
      success: false,
      finalScore: originalScore,
      taskId,
      summary,
      error: error.message,
      fallbackToOriginal: true,
    };
  }
}

/**
 * 记录三层审查结果
 */
function logThreeLayerResult(result) {
  const logDir = '.three-layer-logs';
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const logFile = `${logDir}/three-layer-${result.taskId}-${timestamp}.json`;

  fs.writeFileSync(logFile, JSON.stringify(result, null, 2));
  Logger.info(`📄 三层审查日志已保存: ${logFile}`);
}

/**
 * 主函数
 */
async function main() {
  const taskId = process.argv[2];
  const summary = process.argv[3];
  const originalScore = parseInt(process.argv[4]) || 85;

  if (!taskId || !summary) {
    Logger.error(
      '用法: node verify-task-hook.js <taskId> <summary> [originalScore]'
    );
    Logger.error('示例: node verify-task-hook.js "task-123" "任务完成" 95');
    process.exit(1);
  }

  try {
    const result = await executeThreeLayerReview(
      taskId,
      summary,
      originalScore
    );

    // 记录结果
    logThreeLayerResult(result);

    // 输出最终结果
    console.log('\n='.repeat(60));
    console.log('🎯 三层审查后置钩子执行完成');
    console.log('='.repeat(60));

    if (result.skipped) {
      console.log('⏭️ 状态: 跳过三层审查');
      console.log(`📊 最终评分: ${result.finalScore}/100 (原始评分)`);
    } else if (result.success) {
      console.log('✅ 状态: 三层审查通过');
      console.log(`📊 最终评分: ${result.finalScore}/100`);
      if (result.improved) {
        console.log(
          `📈 评分提升: ${result.originalScore} → ${result.finalScore}`
        );
      }
    } else {
      console.log('❌ 状态: 三层审查失败');
      console.log(`📊 最终评分: ${result.finalScore}/100`);
      if (result.fallbackToOriginal) {
        console.log('🔄 已回退到原始评分');
      }
    }

    console.log(`📝 任务ID: ${result.taskId}`);
    console.log('='.repeat(60));

    // 根据结果设置退出码
    process.exit(result.success ? 0 : 1);
  } catch (error) {
    Logger.error(`后置钩子执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    Logger.error(`脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

export { executeThreeLayerReview, logThreeLayerResult };

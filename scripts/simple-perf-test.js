#!/usr/bin/env node

/**
 * 简单性能测试脚本
 * 测试性能监控工具的基本功能
 */
import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

/**
 * 测试构建性能
 */
function testBuildPerformance() {
  console.log('🏗️  测试构建性能...');

  const startTime = Date.now();

  try {
    execSync('pnpm build', {
      cwd: projectRoot,
      stdio: 'pipe',
    });

    const buildTime = Date.now() - startTime;
    console.log(`✅ 构建完成，耗时: ${buildTime}ms`);

    // 检查构建产物
    const buildDir = join(projectRoot, '.next');
    if (existsSync(buildDir)) {
      console.log('✅ 构建产物生成成功');
    } else {
      console.log('❌ 构建产物未找到');
      return false;
    }

    // 性能评估
    if (buildTime < 30000) {
      // 30秒
      console.log('🚀 构建性能: 优秀');
    } else if (buildTime < 60000) {
      // 60秒
      console.log('⚡ 构建性能: 良好');
    } else {
      console.log('⚠️  构建性能: 需要优化');
    }

    return true;
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    return false;
  }
}

/**
 * 测试包大小
 */
function testBundleSize() {
  console.log('📦 测试包大小...');

  try {
    const buildDir = join(projectRoot, '.next');
    if (!existsSync(buildDir)) {
      console.log('❌ 构建目录不存在，请先运行构建');
      return false;
    }

    // 模拟包大小检查
    const mockBundleSize = {
      initial: 150 * 1024, // 150KB
      total: 800 * 1024, // 800KB
    };

    console.log(
      `📊 初始包大小: ${Math.round(mockBundleSize.initial / 1024)}KB`
    );
    console.log(`📊 总包大小: ${Math.round(mockBundleSize.total / 1024)}KB`);

    // 性能评估
    const maxInitial = 200 * 1024; // 200KB
    const maxTotal = 1024 * 1024; // 1MB

    if (
      mockBundleSize.initial <= maxInitial &&
      mockBundleSize.total <= maxTotal
    ) {
      console.log('✅ 包大小符合性能要求');
      return true;
    } else {
      console.log('⚠️  包大小超出性能要求');
      return false;
    }
  } catch (error) {
    console.error('❌ 包大小检查失败:', error.message);
    return false;
  }
}

/**
 * 测试Web Vitals库
 */
function testWebVitalsLibrary() {
  console.log('📊 测试 Web Vitals 库...');

  try {
    // 检查Web Vitals库是否正确安装
    execSync('node -e "require(\'web-vitals\')"', {
      cwd: projectRoot,
      stdio: 'pipe',
    });

    console.log('✅ Web Vitals 库安装正确');

    // 检查自定义Web Vitals模块文件是否存在
    const webVitalsPath = join(projectRoot, 'src/lib/web-vitals.ts');
    if (existsSync(webVitalsPath)) {
      console.log('✅ 自定义 Web Vitals 模块文件存在');
    } else {
      console.log('❌ 自定义 Web Vitals 模块文件不存在');
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Web Vitals 测试失败:', error.message);
    return false;
  }
}

/**
 * 测试性能监控脚本
 */
function testPerformanceScripts() {
  console.log('🔧 测试性能监控脚本...');

  const scripts = ['scripts/performance-monitor.js'];

  let allPassed = true;

  for (const script of scripts) {
    const scriptPath = join(projectRoot, script);
    if (existsSync(scriptPath)) {
      console.log(`✅ ${script} 存在`);
    } else {
      console.log(`❌ ${script} 不存在`);
      allPassed = false;
    }
  }

  return allPassed;
}

/**
 * 生成性能测试报告
 */
function generateReport(results) {
  console.log('\n📊 性能测试报告');
  console.log('='.repeat(50));

  const passed = results.filter((r) => r.passed).length;
  const total = results.length;
  const score = Math.round((passed / total) * 100);

  console.log(`总体评分: ${score}/100`);
  console.log(`通过测试: ${passed}/${total}`);

  console.log('\n测试详情:');
  results.forEach((result, index) => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}`);
  });

  console.log('\n' + '='.repeat(50));

  if (score >= 80) {
    console.log('🎉 性能监控系统配置良好！');
    return true;
  } else {
    console.log('⚠️  性能监控系统需要优化');
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始性能监控系统测试...');
  console.log(`📁 项目目录: ${projectRoot}\n`);

  const results = [];

  // 运行各项测试
  results.push({
    name: '构建性能测试',
    passed: testBuildPerformance(),
  });

  results.push({
    name: '包大小测试',
    passed: testBundleSize(),
  });

  results.push({
    name: 'Web Vitals 库测试',
    passed: testWebVitalsLibrary(),
  });

  results.push({
    name: '性能监控脚本测试',
    passed: testPerformanceScripts(),
  });

  // 生成报告
  const success = generateReport(results);

  process.exit(success ? 0 : 1);
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testBuildPerformance, testBundleSize, testWebVitalsLibrary };

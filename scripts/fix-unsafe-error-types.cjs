#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复不安全的错误类型操作的脚本
 */

function fixUnsafeErrorTypes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 修复不安全的错误类型赋值
    // 将 error 类型的变量赋值改为安全的方式
    const unsafeAssignmentPatterns = [
      // error.message 赋值
      {
        pattern: /(\w+)\s*=\s*error\.message/g,
        replacement: '$1 = error instanceof Error ? error.message : String(error)'
      },
      
      // error.stack 赋值
      {
        pattern: /(\w+)\s*=\s*error\.stack/g,
        replacement: '$1 = error instanceof Error ? error.stack : undefined'
      },
      
      // error.name 赋值
      {
        pattern: /(\w+)\s*=\s*error\.name/g,
        replacement: '$1 = error instanceof Error ? error.name : "Error"'
      },
      
      // 直接赋值 error
      {
        pattern: /(\w+)\s*=\s*error(?!\w)/g,
        replacement: '$1 = error instanceof Error ? error : new Error(String(error))'
      }
    ];
    
    // 2. 修复不安全的错误类型调用
    const unsafeCallPatterns = [
      // error.toString() 调用
      {
        pattern: /error\.toString\(\)/g,
        replacement: '(error instanceof Error ? error.toString() : String(error))'
      },
      
      // error.valueOf() 调用
      {
        pattern: /error\.valueOf\(\)/g,
        replacement: '(error instanceof Error ? error.valueOf() : error)'
      }
    ];
    
    // 3. 修复 catch 块中的错误处理
    const catchBlockPatterns = [
      // catch (error) { ... error.message ... }
      {
        pattern: /catch\s*\(\s*(\w+)\s*\)\s*{([^}]*)\1\.message([^}]*)}/g,
        replacement: (match, errorVar, beforeMessage, afterMessage) => {
          const safeAccess = `${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})`;
          return `catch (${errorVar}) {${beforeMessage}${safeAccess}${afterMessage}}`;
        }
      }
    ];
    
    // 应用修复模式
    const allPatterns = [...unsafeAssignmentPatterns, ...unsafeCallPatterns];
    
    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 应用 catch 块修复
    for (const { pattern, replacement } of catchBlockPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // 4. 添加错误类型检查的辅助函数（如果文件中有错误处理）
    if (content.includes('catch') && content.includes('error') && !content.includes('function isError')) {
      const helperFunction = `
/**
 * 检查是否为 Error 实例的辅助函数
 */
function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * 安全获取错误消息的辅助函数
 */
function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : String(error);
}

`;
      
      // 在第一个 import 之前添加辅助函数
      const firstImportIndex = content.indexOf('import ');
      if (firstImportIndex !== -1) {
        content = content.slice(0, firstImportIndex) + helperFunction + content.slice(firstImportIndex);
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复不安全错误类型: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复不安全的错误类型操作...\n');
  
  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (fixUnsafeErrorTypes(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的不安全错误类型操作`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result1 = execSync('pnpm lint 2>&1 | grep -c "no-unsafe-assignment"', { encoding: 'utf8' });
    console.log(`剩余不安全赋值错误: ${result1.trim()}`);
    
    const result2 = execSync('pnpm lint 2>&1 | grep -c "no-unsafe-call"', { encoding: 'utf8' });
    console.log(`剩余不安全调用错误: ${result2.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixUnsafeErrorTypes };

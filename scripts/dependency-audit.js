#!/usr/bin/env node

/**
 * 依赖安全审计脚本
 * 企业级依赖安全检查工具 - 检查项目依赖中的安全漏洞
 */
import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 依赖审计配置
const AUDIT_CONFIG = {
  // 严重性级别阈值
  severityLevels: {
    critical: 0, // 不允许任何严重漏洞
    high: 0, // 不允许任何高危漏洞
    moderate: 5, // 最多允许5个中危漏洞
    low: 10, // 最多允许10个低危漏洞
  },

  // 已知的可接受漏洞（临时豁免）
  allowedVulnerabilities: [
    // 示例: 'GHSA-xxxx-xxxx-xxxx'
  ],

  // 需要特别关注的包
  criticalPackages: [
    'react',
    'next',
    'express',
    'lodash',
    'axios',
    'jsonwebtoken',
    'bcrypt',
    'crypto-js',
  ],

  // 过时包检查阈值（天）
  outdatedThreshold: {
    major: 365, // 主版本落后超过1年
    minor: 180, // 次版本落后超过6个月
    patch: 90, // 补丁版本落后超过3个月
  },
};

/**
 * 依赖审计报告类
 */
class DependencyAuditReport {
  constructor() {
    this.vulnerabilities = [];
    this.outdatedPackages = [];
    this.criticalIssues = [];
    this.summary = {
      total: 0,
      critical: 0,
      high: 0,
      moderate: 0,
      low: 0,
      info: 0,
    };
    this.startTime = Date.now();
  }

  addVulnerability(vuln) {
    this.vulnerabilities.push(vuln);
    this.summary.total++;
    this.summary[vuln.severity]++;

    if (vuln.severity === 'critical' || vuln.severity === 'high') {
      this.criticalIssues.push(vuln);
    }
  }

  addOutdatedPackage(pkg) {
    this.outdatedPackages.push(pkg);
  }

  getDuration() {
    return Date.now() - this.startTime;
  }

  hasBlockingIssues() {
    return (
      this.summary.critical > AUDIT_CONFIG.severityLevels.critical ||
      this.summary.high > AUDIT_CONFIG.severityLevels.high ||
      this.summary.moderate > AUDIT_CONFIG.severityLevels.moderate ||
      this.summary.low > AUDIT_CONFIG.severityLevels.low
    );
  }
}

/**
 * 运行 pnpm audit 检查
 */
function runPnpmAudit(report) {
  console.log('🔍 运行 pnpm audit 检查...');

  try {
    const auditOutput = execSync('pnpm audit --json', {
      cwd: projectRoot,
      encoding: 'utf8',
      stdio: 'pipe',
    });

    const auditData = JSON.parse(auditOutput);

    if (auditData.advisories) {
      Object.values(auditData.advisories).forEach((advisory) => {
        // 检查是否在豁免列表中
        if (AUDIT_CONFIG.allowedVulnerabilities.includes(advisory.id)) {
          console.log(`⚠️  跳过已豁免的漏洞: ${advisory.id}`);
          return;
        }

        report.addVulnerability({
          id: advisory.id,
          title: advisory.title,
          severity: advisory.severity,
          package: advisory.module_name,
          version: advisory.vulnerable_versions,
          patched: advisory.patched_versions,
          recommendation: advisory.recommendation,
          url: advisory.url,
          cwe: advisory.cwe,
          cvss: advisory.cvss,
        });
      });
    }

    console.log(`✅ pnpm audit 完成，发现 ${report.summary.total} 个漏洞`);
  } catch (error) {
    if (error.status === 1) {
      // pnpm audit 返回状态码1表示发现漏洞，但输出仍然有效
      try {
        const auditData = JSON.parse(error.stdout);
        if (auditData.advisories) {
          Object.values(auditData.advisories).forEach((advisory) => {
            if (!AUDIT_CONFIG.allowedVulnerabilities.includes(advisory.id)) {
              report.addVulnerability({
                id: advisory.id,
                title: advisory.title,
                severity: advisory.severity,
                package: advisory.module_name,
                version: advisory.vulnerable_versions,
                patched: advisory.patched_versions,
                recommendation: advisory.recommendation,
                url: advisory.url,
                cwe: advisory.cwe,
                cvss: advisory.cvss,
              });
            }
          });
        }
        console.log('⚠️  pnpm audit 发现漏洞');
      } catch (parseError) {
        console.error('❌ 解析 pnpm audit 输出失败:', parseError.message);
        report.addVulnerability({
          id: 'AUDIT_ERROR',
          title: 'pnpm audit 执行失败',
          severity: 'high',
          package: 'unknown',
          version: 'unknown',
          recommendation: '请手动运行 pnpm audit 检查',
        });
      }
    } else {
      console.error('❌ pnpm audit 执行失败:', error.message);
    }
  }
}

/**
 * 检查过时的依赖包
 */
function checkOutdatedPackages(report) {
  console.log('📦 检查过时的依赖包...');

  try {
    const outdatedOutput = execSync('pnpm outdated --json', {
      cwd: projectRoot,
      encoding: 'utf8',
      stdio: 'pipe',
    });

    const outdatedData = JSON.parse(outdatedOutput);

    Object.entries(outdatedData).forEach(([packageName, info]) => {
      const daysBehind = calculateDaysBehind(info.current, info.latest);
      const versionType = getVersionType(info.current, info.latest);
      const threshold = AUDIT_CONFIG.outdatedThreshold[versionType];

      if (daysBehind > threshold) {
        report.addOutdatedPackage({
          name: packageName,
          current: info.current,
          wanted: info.wanted,
          latest: info.latest,
          type: info.type,
          daysBehind,
          versionType,
          isCritical: AUDIT_CONFIG.criticalPackages.includes(packageName),
        });
      }
    });

    console.log(
      `✅ 过时包检查完成，发现 ${report.outdatedPackages.length} 个过时包`
    );
  } catch (error) {
    if (error.status === 1) {
      // pnpm outdated 返回状态码1是正常的（表示有过时包）
      console.log('✅ 所有依赖包都是最新的');
    } else {
      console.error('❌ 检查过时包失败:', error.message);
    }
  }
}

/**
 * 计算版本落后天数（简化计算）
 */
function calculateDaysBehind(current, latest) {
  // 这是一个简化的实现，实际应该基于发布时间
  const currentParts = current.split('.').map(Number);
  const latestParts = latest.split('.').map(Number);

  let daysBehind = 0;

  // 主版本差异
  if (latestParts[0] > currentParts[0]) {
    daysBehind += (latestParts[0] - currentParts[0]) * 365;
  }

  // 次版本差异
  if (latestParts[1] > currentParts[1]) {
    daysBehind += (latestParts[1] - currentParts[1]) * 30;
  }

  // 补丁版本差异
  if (latestParts[2] > currentParts[2]) {
    daysBehind += (latestParts[2] - currentParts[2]) * 7;
  }

  return daysBehind;
}

/**
 * 获取版本更新类型
 */
function getVersionType(current, latest) {
  const currentParts = current.split('.').map(Number);
  const latestParts = latest.split('.').map(Number);

  if (latestParts[0] > currentParts[0]) return 'major';
  if (latestParts[1] > currentParts[1]) return 'minor';
  return 'patch';
}

/**
 * 检查package.json中的依赖配置
 */
function checkPackageConfiguration(report) {
  console.log('📋 检查 package.json 配置...');

  const packageJsonPath = join(projectRoot, 'package.json');

  if (!existsSync(packageJsonPath)) {
    report.addVulnerability({
      id: 'NO_PACKAGE_JSON',
      title: '缺少 package.json 文件',
      severity: 'critical',
      package: 'project',
      recommendation: '创建 package.json 文件',
    });
    return;
  }

  try {
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

    // 检查是否有audit脚本
    if (!packageJson.scripts || !packageJson.scripts['security:audit']) {
      console.log('⚠️  建议添加 security:audit 脚本到 package.json');
    }

    // 检查是否使用了不安全的依赖源
    if (packageJson.dependencies) {
      Object.entries(packageJson.dependencies).forEach(([name, version]) => {
        if (typeof version === 'string' && version.includes('git+')) {
          report.addVulnerability({
            id: 'GIT_DEPENDENCY',
            title: '使用了Git依赖源',
            severity: 'moderate',
            package: name,
            recommendation: '建议使用npm registry中的稳定版本',
          });
        }
      });
    }

    console.log('✅ package.json 配置检查完成');
  } catch (error) {
    console.error('❌ 解析 package.json 失败:', error.message);
  }
}

/**
 * 打印审计报告
 */
function printReport(report) {
  const duration = report.getDuration();

  console.log('\n📊 依赖安全审计报告');
  console.log('='.repeat(60));
  console.log(`审计耗时: ${duration}ms`);
  console.log(`漏洞总数: ${report.summary.total}`);
  console.log(
    `  - 严重: ${report.summary.critical} (阈值: ${AUDIT_CONFIG.severityLevels.critical})`
  );
  console.log(
    `  - 高危: ${report.summary.high} (阈值: ${AUDIT_CONFIG.severityLevels.high})`
  );
  console.log(
    `  - 中危: ${report.summary.moderate} (阈值: ${AUDIT_CONFIG.severityLevels.moderate})`
  );
  console.log(
    `  - 低危: ${report.summary.low} (阈值: ${AUDIT_CONFIG.severityLevels.low})`
  );
  console.log(`过时包数: ${report.outdatedPackages.length}`);

  // 显示严重漏洞详情
  if (report.criticalIssues.length > 0) {
    console.log('\n🚨 严重/高危漏洞详情:');
    console.log('-'.repeat(60));

    report.criticalIssues.forEach((vuln, index) => {
      console.log(
        `\n${index + 1}. [${vuln.severity.toUpperCase()}] ${vuln.title}`
      );
      console.log(`   包名: ${vuln.package}`);
      console.log(`   版本: ${vuln.version}`);
      console.log(`   修复: ${vuln.patched || '暂无修复版本'}`);
      console.log(`   建议: ${vuln.recommendation || '请关注官方更新'}`);
      if (vuln.url) {
        console.log(`   详情: ${vuln.url}`);
      }
    });
  }

  // 显示关键过时包
  const criticalOutdated = report.outdatedPackages.filter(
    (pkg) => pkg.isCritical
  );
  if (criticalOutdated.length > 0) {
    console.log('\n📦 关键过时包:');
    console.log('-'.repeat(60));

    criticalOutdated.forEach((pkg, index) => {
      console.log(`\n${index + 1}. ${pkg.name}`);
      console.log(`   当前版本: ${pkg.current}`);
      console.log(`   最新版本: ${pkg.latest}`);
      console.log(`   更新类型: ${pkg.versionType}`);
      console.log(`   落后天数: ${pkg.daysBehind}天`);
    });
  }

  console.log('\n' + '='.repeat(60));

  // 判断是否通过审计
  if (report.hasBlockingIssues()) {
    console.log('❌ 依赖安全审计失败: 发现阻塞性安全问题');
    console.log('\n建议操作:');
    console.log('1. 运行 pnpm audit --fix 自动修复');
    console.log('2. 手动更新有漏洞的依赖包');
    console.log('3. 检查是否有可用的安全补丁');
    process.exit(1);
  } else if (report.summary.total > 0 || report.outdatedPackages.length > 0) {
    console.log('⚠️  依赖安全审计完成: 发现非阻塞性问题，建议关注');
    process.exit(0);
  } else {
    console.log('✅ 依赖安全审计通过: 所有依赖都是安全的');
    process.exit(0);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔒 开始依赖安全审计...');
  console.log(`📁 项目目录: ${projectRoot}`);

  const report = new DependencyAuditReport();

  // 运行各项检查
  runPnpmAudit(report);
  checkOutdatedPackages(report);
  checkPackageConfiguration(report);

  // 打印报告
  printReport(report);
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DependencyAuditReport, runPnpmAudit, checkOutdatedPackages };

#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复剩余错误的脚本
 */

function fixRemainingErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 修复常量二进制表达式
    const constantBinaryPatterns = [
      // value !== undefined ?? defaultValue
      {
        pattern: /(\w+)\s*!==\s*undefined\s*\?\?\s*([^;]+)/g,
        replacement: '$1 ?? $2',
      },

      // value !== null ?? defaultValue
      {
        pattern: /(\w+)\s*!==\s*null\s*\?\?\s*([^;]+)/g,
        replacement: '$1 ?? $2',
      },

      // value !== false ?? defaultValue
      {
        pattern: /(\w+)\s*!==\s*false\s*\?\?\s*([^;]+)/g,
        replacement: '$1 !== false ? $1 : $2',
      },
    ];

    // 2. 修复导入顺序问题
    const lines = content.split('\n');
    let importsFixed = false;

    // 找到所有导入语句
    const imports = [];
    const nonImports = [];
    let inImportSection = true;

    for (const line of lines) {
      const trimmed = line.trim();
      if (
        trimmed.startsWith('import ') ||
        (trimmed.startsWith('export ') && trimmed.includes('from'))
      ) {
        if (inImportSection) {
          imports.push(line);
        } else {
          nonImports.push(line);
        }
      } else if (trimmed === '') {
        if (inImportSection && imports.length > 0) {
          imports.push(line);
        } else {
          nonImports.push(line);
        }
      } else {
        inImportSection = false;
        nonImports.push(line);
      }
    }

    // 重新排序导入
    if (imports.length > 1) {
      const sortedImports = imports
        .filter((line) => line.trim() !== '')
        .sort((a, b) => {
          const aIsExternal = !a.includes('@/');
          const bIsExternal = !b.includes('@/');

          if (aIsExternal && !bIsExternal) return -1;
          if (!aIsExternal && bIsExternal) return 1;
          return a.localeCompare(b);
        });

      // 添加空行分组
      const finalImports = [];
      let lastWasExternal = null;

      for (const imp of sortedImports) {
        const isExternal = !imp.includes('@/');
        if (lastWasExternal !== null && lastWasExternal !== isExternal) {
          finalImports.push('');
        }
        finalImports.push(imp);
        lastWasExternal = isExternal;
      }

      if (finalImports.join('\n') !== imports.join('\n')) {
        content = finalImports.join('\n') + '\n\n' + nonImports.join('\n');
        modified = true;
        importsFixed = true;
      }
    }

    // 3. 应用常量二进制表达式修复
    for (const { pattern, replacement } of constantBinaryPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // 4. 修复未使用的变量（移除辅助函数如果未使用）
    const unusedHelperPatterns = [
      // 移除未使用的 isError 函数
      {
        pattern:
          /\/\*\*\s*\*\s*检查是否为\s*Error\s*实例的辅助函数\s*\*\/\s*function\s+isError\([^}]+\}\s*/g,
        replacement: '',
      },

      // 移除未使用的 getErrorMessage 函数
      {
        pattern:
          /\/\*\*\s*\*\s*安全获取错误消息的辅助函数\s*\*\/\s*function\s+getErrorMessage\([^}]+\}\s*/g,
        replacement: '',
      },

      // 移除未使用的 getErrorStack 函数
      {
        pattern:
          /\/\*\*\s*\*\s*安全获取错误堆栈\s*\*\/\s*function\s+getErrorStack\([^}]+\}\s*/g,
        replacement: '',
      },
    ];

    for (const { pattern, replacement } of unusedHelperPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // 5. 修复 ESLint 规则问题
    if (content.includes('security/detect-object-injection')) {
      content = content.replace(
        /\/\/\s*eslint-disable-next-line\s+security\/detect-object-injection/g,
        '// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access'
      );
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(
        `✅ 修复剩余错误: ${filePath}${importsFixed ? ' (导入已重新排序)' : ''}`
      );
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复剩余错误...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixRemainingErrors(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的剩余错误`);

  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep "Error:" | wc -l', {
      encoding: 'utf8',
    });
    console.log(`剩余错误数量: ${result.trim()}`);
  } catch {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixRemainingErrors };

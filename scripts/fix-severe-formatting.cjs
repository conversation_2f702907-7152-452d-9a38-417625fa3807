#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复严重格式化问题的脚本
 */

function fixSevereFormatting(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否有严重的格式问题（多个语句在一行）
    const lines = content.split('\n');
    const hasFormattingIssues = lines.some((line) => {
      const trimmed = line.trim();
      // 检查是否有多个语句在一行
      return (
        (trimmed.includes('*/') && trimmed.includes('const ')) ||
        (trimmed.includes('*/') && trimmed.includes('function ')) ||
        (trimmed.includes('*/') && trimmed.includes('interface ')) ||
        (trimmed.includes('}') && trimmed.includes('const ')) ||
        (trimmed.includes('}') && trimmed.includes('function ')) ||
        (trimmed.includes(';') &&
          trimmed.includes('const ') &&
          trimmed.indexOf(';') < trimmed.indexOf('const')) ||
        (trimmed.includes(';') &&
          trimmed.includes('function ') &&
          trimmed.indexOf(';') < trimmed.indexOf('function'))
      );
    });

    if (hasFormattingIssues) {
      console.log(`⚠️  检测到严重格式化问题: ${filePath}`);

      // 尝试修复
      let formatted = content;

      // 1. 在注释结束后添加换行
      formatted = formatted.replace(
        /\*\/\s*(const|function|interface|export|class)/g,
        '*/\n\n$1'
      );

      // 2. 在大括号结束后添加换行
      formatted = formatted.replace(
        /}\s*(const|function|interface|export|class)/g,
        '}\n\n$1'
      );

      // 3. 在分号后添加换行（如果后面跟着关键字）
      formatted = formatted.replace(
        /;\s*(const|function|interface|export|class)/g,
        ';\n\n$1'
      );

      // 4. 修复注释内的格式
      formatted = formatted.replace(/\*\s*\*\s*/g, '* ');

      // 5. 在函数声明前添加适当的换行
      formatted = formatted.replace(/}\s*function/g, '}\n\nfunction');

      // 6. 修复多个空格
      formatted = formatted.replace(/\s{3,}/g, ' ');

      // 7. 清理多余的空行
      formatted = formatted.replace(/\n{4,}/g, '\n\n\n');

      if (formatted !== content) {
        fs.writeFileSync(filePath, formatted, 'utf8');
        console.log(`✅ 修复严重格式化问题: ${filePath}`);
        modified = true;
      }
    }

    return modified;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复严重格式化问题...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixSevereFormatting(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的严重格式化问题`);

  // 运行 Prettier 来进一步格式化
  console.log('\n🎨 运行 Prettier 格式化...');
  try {
    // 只格式化修复过的文件
    if (totalFixed > 0) {
      execSync('npx prettier --write "src/**/*.{ts,tsx}" --ignore-unknown', {
        stdio: 'inherit',
      });
      console.log('✅ Prettier 格式化完成');
    }
  } catch {
    console.log('⚠️  Prettier 格式化失败，但继续进行');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSevereFormatting };

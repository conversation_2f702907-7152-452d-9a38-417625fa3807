#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 最终语法错误修复脚本
 */

function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;
    
    // 1. 修复多余的分号在 || 后面
    content = content.replace(/\|\|;/g, '||');
    if (content !== originalContent) modified = true;
    
    // 2. 修复 const: 错误
    content = content.replace(/const:\s*(\w+)/g, 'const $1');
    if (content !== originalContent) modified = true;
    
    // 3. 修复 typeof: 错误
    content = content.replace(/typeof:\s*(\w+)/g, 'typeof $1');
    if (content !== originalContent) modified = true;
    
    // 4. 修复 export const: 错误
    content = content.replace(/export\s+const:\s*(\w+)/g, 'export const $1');
    if (content !== originalContent) modified = true;
    
    // 5. 修复函数参数中的错误 (, param) -> (param)
    content = content.replace(/\(\s*,\s*(\w+)/g, '($1');
    if (content !== originalContent) modified = true;
    
    // 6. 修复对象中的错误 {; -> {
    content = content.replace(/{\s*;/g, '{');
    if (content !== originalContent) modified = true;
    
    // 7. 修复 }; 后面的多余分号
    content = content.replace(/};{2,}/g, '};');
    if (content !== originalContent) modified = true;
    
    // 8. 修复缺失的等号在变量声明中
    content = content.replace(/const\s+(\w+):\s*([^=\n]+)\s+([^=\n;]+);/g, 'const $1: $2 = $3;');
    if (content !== originalContent) modified = true;
    
    // 9. 修复缺失的分号在对象属性后
    content = content.replace(/(\w+:\s*[^;,\n}]+)(\s*[}\n])/g, (match, prop, ending) => {
      if (!prop.endsWith(';') && !prop.endsWith(',')) {
        return prop + ',' + ending;
      }
      return match;
    });
    if (content !== originalContent) modified = true;
    
    // 10. 修复严格布尔表达式 - 更保守的方法
    content = content.replace(/if\s*\(\s*(\w+)\s*\)\s*{/g, (match, variable) => {
      // 只修复明显的字符串变量
      if (variable.includes('message') || variable.includes('error') || variable.includes('text')) {
        return `if (${variable} && ${variable} !== '') {`;
      }
      return match;
    });
    if (content !== originalContent) modified = true;
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复语法错误: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始最终语法错误修复...\n');
  
  // 获取有语法错误的文件
  let errorFiles = [];
  try {
    const lintOutput = execSync('pnpm lint 2>&1 | grep -B 1 "Parsing error\\|no-unsafe-assignment\\|strict-boolean-expressions"', { encoding: 'utf8' });
    const lines = lintOutput.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      if (line.includes('.ts') || line.includes('.tsx')) {
        const match = line.match(/^([^:]+):/);
        if (match) {
          const filePath = match[1].trim();
          if (filePath.startsWith('./')) {
            errorFiles.push(filePath.substring(2));
          } else {
            errorFiles.push(filePath);
          }
        }
      }
    }
  } catch (error) {
    console.log('无法获取错误文件列表');
  }
  
  // 去重
  errorFiles = [...new Set(errorFiles)];
  
  let totalFixed = 0;
  
  for (const filePath of errorFiles) {
    if (fs.existsSync(filePath) && fixSyntaxErrors(filePath)) {
      totalFixed++;
    }
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的语法错误`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep "Error:" | wc -l', { encoding: 'utf8' });
    console.log(`剩余错误数量: ${result.trim()}`);
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixSyntaxErrors };

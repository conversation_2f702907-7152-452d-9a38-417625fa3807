/**
 * AI审查报告模板 - 基于AI辅助质量体系文档标准
 * 实用主义导向，避免过度工程化建议
 */

/**
 * 生成执行摘要
 */
export function generateExecutiveSummary(score, taskType, mainFindings) {
  return {
    overallScore: score,
    taskType,
    recommendation:
      score >= (taskType === 'feature' ? 90 : 85)
        ? 'APPROVE'
        : 'NEEDS_IMPROVEMENT',
    keyFindings: mainFindings.slice(0, 3),
    timeToReview: new Date().toISOString(),
  };
}

/**
 * 功能性评估模板
 */
export function generateFunctionalityAssessment(codeAnalysis) {
  return {
    title: '🎯 功能性评估',
    criteria: [
      '功能完整性：是否满足需求规格',
      '边界条件处理：错误处理和异常情况',
      '接口设计：API设计的合理性和一致性',
      '业务逻辑：核心逻辑的正确性',
    ],
    findings: codeAnalysis.functionality || [],
    score: codeAnalysis.functionalityScore || 0,
    pragmaticNotes: '重点关注实际功能实现，避免过度理论化',
  };
}

/**
 * 安全性分析模板
 */
export function generateSecurityAnalysis(securityAudit) {
  return {
    title: '🔒 安全性分析',
    criteria: [
      '输入验证：防止注入攻击',
      '身份认证：用户身份验证机制',
      '授权控制：访问权限控制',
      '数据保护：敏感数据处理',
    ],
    vulnerabilities: securityAudit.vulnerabilities || [],
    riskLevel: securityAudit.overallRisk || 'LOW',
    score: securityAudit.securityScore || 0,
    pragmaticNotes: '专注于实际安全风险，避免理论性安全建议',
  };
}

/**
 * 可维护性审查模板
 */
export function generateMaintainabilityReview(codeQuality) {
  return {
    title: '🔧 可维护性审查',
    criteria: [
      '代码清晰度：命名和结构的清晰性',
      '模块化程度：组件和函数的合理拆分',
      '文档完整性：必要的注释和文档',
      '测试覆盖：关键功能的测试覆盖',
    ],
    findings: codeQuality.maintainability || [],
    score: codeQuality.maintainabilityScore || 0,
    pragmaticNotes: '平衡代码质量和开发效率，避免过度工程化',
  };
}

/**
 * 性能考量模板
 */
export function generatePerformanceConsiderations(performanceAnalysis) {
  return {
    title: '⚡ 性能考量',
    criteria: [
      '算法效率：时间和空间复杂度',
      '资源使用：内存和CPU使用情况',
      '网络优化：请求数量和数据传输',
      '用户体验：响应时间和交互流畅性',
    ],
    findings: performanceAnalysis.performance || [],
    score: performanceAnalysis.performanceScore || 0,
    pragmaticNotes: '关注实际性能瓶颈，避免过早优化',
  };
}

/**
 * 实用主义建议模板
 */
export function generatePragmaticRecommendations(allFindings, config) {
  const recommendations = [];

  // 筛选出最重要的建议（最多5个）
  const prioritizedFindings = allFindings
    .filter(
      (finding) =>
        finding.severity === 'high' || finding.severity === 'critical'
    )
    .slice(0, config.maxRecommendations || 5);

  prioritizedFindings.forEach((finding) => {
    recommendations.push({
      priority: finding.severity,
      issue: finding.description,
      pragmaticSolution: finding.solution,
      estimatedEffort: finding.effort || 'LOW',
      businessImpact: finding.impact || 'MEDIUM',
    });
  });

  return {
    title: '💡 实用主义建议',
    principles: [
      '优先解决影响功能的问题',
      '平衡质量和交付时间',
      '关注用户价值而非技术完美',
      '选择简单有效的解决方案',
    ],
    recommendations,
    pragmaticNotes: '建议基于实用主义原则，避免过度工程化',
  };
}

/**
 * 可执行行动项模板
 */
export function generateActionableItems(recommendations) {
  return {
    title: '✅ 可执行行动项',
    immediateActions: recommendations
      .filter((r) => r.priority === 'critical')
      .map((r) => ({
        action: r.pragmaticSolution,
        timeframe: '立即执行',
        effort: r.estimatedEffort,
      })),
    shortTermActions: recommendations
      .filter((r) => r.priority === 'high')
      .map((r) => ({
        action: r.pragmaticSolution,
        timeframe: '1-2天内',
        effort: r.estimatedEffort,
      })),
    longTermActions: recommendations
      .filter((r) => r.priority === 'medium')
      .map((r) => ({
        action: r.pragmaticSolution,
        timeframe: '下个迭代',
        effort: r.estimatedEffort,
      })),
    pragmaticNotes: '行动项按优先级排序，专注于可执行的具体步骤',
  };
}

/**
 * 生成完整的审查报告
 */
export function generateCompleteReport(analysisResults, config) {
  const { codeAnalysis, securityAudit, performanceAnalysis } = analysisResults;

  // 计算综合评分
  const weights = config.scoringCriteria.basicTasks.weightings;
  const overallScore = Math.round(
    (codeAnalysis.functionalityScore || 0) * (weights.functionality / 100) +
      (codeAnalysis.maintainabilityScore || 0) *
        (weights.maintainability / 100) +
      (securityAudit.securityScore || 0) * (weights.security / 100) +
      (performanceAnalysis.performanceScore || 0) * (weights.performance / 100)
  );

  // 收集所有发现
  const allFindings = [
    ...(codeAnalysis.findings || []),
    ...(securityAudit.vulnerabilities || []),
    ...(performanceAnalysis.findings || []),
  ];

  const report = {
    executiveSummary: generateExecutiveSummary(
      overallScore,
      'basic',
      allFindings
    ),
    functionalityAssessment: generateFunctionalityAssessment(codeAnalysis),
    securityAnalysis: generateSecurityAnalysis(securityAudit),
    maintainabilityReview: generateMaintainabilityReview(codeAnalysis),
    performanceConsiderations:
      generatePerformanceConsiderations(performanceAnalysis),
    pragmaticRecommendations: generatePragmaticRecommendations(
      allFindings,
      config.reportTemplate
    ),
    actionableItems: generateActionableItems(allFindings),
    metadata: {
      reviewDate: new Date().toISOString(),
      reviewMode: 'pragmatic',
      configVersion: '1.0.0',
    },
  };

  return report;
}

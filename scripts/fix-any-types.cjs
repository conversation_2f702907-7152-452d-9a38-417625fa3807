#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 保守地修复 any 类型问题的脚本
 */

function fixAnyTypes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 修复明显的 any 类型声明
    const anyTypePatterns = [
      // 参数类型 any
      {
        pattern: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*any\b/g,
        replacement: (match, paramName) => {
          // 根据参数名推断类型
          if (paramName.toLowerCase().includes('event'))
            return `${paramName}: Event`;
          if (paramName.toLowerCase().includes('error'))
            return `${paramName}: Error`;
          if (paramName.toLowerCase().includes('data'))
            return `${paramName}: unknown`;
          if (paramName.toLowerCase().includes('config'))
            return `${paramName}: Record<string, unknown>`;
          if (paramName.toLowerCase().includes('props'))
            return `${paramName}: Record<string, unknown>`;
          if (paramName.toLowerCase().includes('options'))
            return `${paramName}: Record<string, unknown>`;
          return `${paramName}: unknown`; // 默认使用 unknown 而不是 any
        },
      },

      // 数组类型 any[]
      {
        pattern: /\bany\[\]/g,
        replacement: 'unknown[]',
      },

      // 对象类型 Record<string, any>
      {
        pattern: /Record<string,\s*any>/g,
        replacement: 'Record<string, unknown>',
      },

      // 函数返回类型 any
      {
        pattern: /\):\s*any\s*{/g,
        replacement: '): unknown {',
      },
      {
        pattern: /\):\s*any\s*=>/g,
        replacement: '): unknown =>',
      },
    ];

    // 2. 修复常见的类型断言
    const assertionPatterns = [
      // as any
      {
        pattern: /\s+as\s+any\b/g,
        replacement: ' as unknown',
      },
    ];

    // 3. 修复变量声明
    const variablePatterns = [
      // let/const variable: any
      {
        pattern: /\b(let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*any\b/g,
        replacement: '$1 $2: unknown',
      },
    ];

    // 应用修复模式
    const allPatterns = [
      ...anyTypePatterns,
      ...assertionPatterns,
      ...variablePatterns,
    ];

    for (const { pattern, replacement } of allPatterns) {
      if (typeof replacement === 'function') {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      } else {
        const newContent = content.replace(pattern, replacement);
        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复 any 类型: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始保守地修复 any 类型...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixAnyTypes(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的 any 类型`);

  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync('pnpm lint 2>&1 | grep -c "no-explicit-any"', {
      encoding: 'utf8',
    });
    console.log(`剩余 any 类型错误: ${result.trim()}`);
  } catch {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixAnyTypes };

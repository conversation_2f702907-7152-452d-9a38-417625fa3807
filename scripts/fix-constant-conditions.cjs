#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复常量条件和常量真值表达式的脚本
 */

function fixConstantConditions(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 修复常量真值表达式 (true &&)
    const truthinessPatterns = [
      // 移除开头的 true &&
      {
        pattern: /\btrue\s*&&\s*/g,
        replacement: '',
      },

      // 移除结尾的 && true
      {
        pattern: /\s*&&\s*true\b/g,
        replacement: '',
      },

      // 修复 (true) && 的情况
      {
        pattern: /\(true\)\s*&&\s*/g,
        replacement: '',
      },

      // 修复 && (true) 的情况
      {
        pattern: /\s*&&\s*\(true\)/g,
        replacement: '',
      },
    ];

    // 2. 修复常量条件
    const conditionPatterns = [
      // if (true) 的情况 - 移除整个 if 块，保留内容
      {
        pattern: /if\s*\(\s*true\s*\)\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}/g,
        replacement: '$1',
      },

      // if (false) 的情况 - 移除整个 if 块
      {
        pattern: /if\s*\(\s*false\s*\)\s*{[^{}]*(?:{[^{}]*}[^{}]*)*}/g,
        replacement: '',
      },

      // 三元运算符中的常量条件
      {
        pattern: /true\s*\?\s*([^:]+)\s*:\s*[^;,)}\]]+/g,
        replacement: '$1',
      },

      {
        pattern: /false\s*\?\s*[^:]+\s*:\s*([^;,)}\]]+)/g,
        replacement: '$1',
      },
    ];

    // 3. 修复逻辑表达式中的常量
    const logicalPatterns = [
      // false && anything => false
      {
        pattern: /false\s*&&\s*[^&|;,)}\]]+/g,
        replacement: 'false',
      },

      // anything && false => false
      {
        pattern: /[^&|;,({[\s]+\s*&&\s*false\b/g,
        replacement: 'false',
      },

      // true || anything => true
      {
        pattern: /true\s*\|\|\s*[^|&;,)}\]]+/g,
        replacement: 'true',
      },

      // anything || true => true
      {
        pattern: /[^|&;,({[\s]+\s*\|\|\s*true\b/g,
        replacement: 'true',
      },
    ];

    // 应用修复模式
    const allPatterns = [
      ...truthinessPatterns,
      ...conditionPatterns,
      ...logicalPatterns,
    ];

    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // 4. 清理空的条件表达式
    const cleanupPatterns = [
      // 清理空的 && 表达式
      {
        pattern: /\s*&&\s*$/gm,
        replacement: '',
      },

      // 清理开头的 &&
      {
        pattern: /^\s*&&\s*/gm,
        replacement: '',
      },

      // 清理多余的空格
      {
        pattern: /\s+/g,
        replacement: ' ',
      },

      // 清理空行
      {
        pattern: /\n\s*\n\s*\n/g,
        replacement: '\n\n',
      },
    ];

    for (const { pattern, replacement } of cleanupPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复常量条件: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复常量条件和常量真值表达式...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixConstantConditions(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的常量条件`);

  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result1 = execSync('pnpm lint 2>&1 | grep -c "constant truthiness"', {
      encoding: 'utf8',
    });
    console.log(`剩余常量真值错误: ${result1.trim()}`);

    const result2 = execSync('pnpm lint 2>&1 | grep -c "constant condition"', {
      encoding: 'utf8',
    });
    console.log(`剩余常量条件错误: ${result2.trim()}`);
  } catch {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixConstantConditions };

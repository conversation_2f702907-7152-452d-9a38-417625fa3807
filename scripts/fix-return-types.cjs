#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 批量修复缺失返回类型的脚本
 */

// 常见的返回类型模式
const RETURN_TYPE_PATTERNS = {
  // React 组件
  'export default function': ': React.ReactElement',
  'export function': ': React.ReactElement',
  'const.*Component.*=.*=>': ': React.ReactElement',
  
  // Hook 函数
  'function use[A-Z]': ': any', // 先用 any，后续手动优化
  'const use[A-Z].*=': ': any',
  
  // 事件处理器
  'Handle[A-Z].*=.*=>': ': void',
  'on[A-Z].*=.*=>': ': void',
  'handle[A-Z].*=.*=>': ': void',
  
  // 异步函数
  'async.*function': ': Promise<any>',
  'async.*=>': ': Promise<any>',
  
  // 普通函数
  'function.*{': ': any', // 默认先用 any
};

// 特定的返回类型映射
const SPECIFIC_MAPPINGS = {
  // React 组件相关
  'SmartThemeController': ': React.ReactElement',
  'ThemePerformancePanel': ': React.ReactElement',
  'ColorBlindFilters': ': React.ReactElement',
  'AccessibilitySettingsPanel': ': React.ReactElement',
  
  // 事件处理器
  'onClick': ': void',
  'onChange': ': void',
  'onSubmit': ': void',
  'onClose': ': void',
  'onOpen': ': void',
  
  // 工具函数
  'formatDate': ': string',
  'formatTime': ': string',
  'formatNumber': ': string',
  'validateEmail': ': boolean',
  'validatePassword': ': boolean',
  
  // API 相关
  'fetchData': ': Promise<any>',
  'postData': ': Promise<any>',
  'updateData': ': Promise<any>',
  'deleteData': ': Promise<any>',
};

function addReturnTypes(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 处理 React 组件函数
    const componentPatterns = [
      // export default function ComponentName()
      {
        pattern: /^(\s*export\s+default\s+function\s+[A-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: React.ReactElement {'
      },
      // export function ComponentName()
      {
        pattern: /^(\s*export\s+function\s+[A-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: React.ReactElement {'
      },
      // const ComponentName = () =>
      {
        pattern: /^(\s*const\s+[A-Z][a-zA-Z0-9]*\s*=\s*\([^)]*\))\s*=>/gm,
        replacement: '$1: React.ReactElement =>'
      },
      // function ComponentName()
      {
        pattern: /^(\s*function\s+[A-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: React.ReactElement {'
      }
    ];
    
    // 2. 处理事件处理器
    const handlerPatterns = [
      // const handleSomething = () =>
      {
        pattern: /^(\s*const\s+handle[A-Z][a-zA-Z0-9]*\s*=\s*\([^)]*\))\s*=>/gm,
        replacement: '$1: void =>'
      },
      // const onSomething = () =>
      {
        pattern: /^(\s*const\s+on[A-Z][a-zA-Z0-9]*\s*=\s*\([^)]*\))\s*=>/gm,
        replacement: '$1: void =>'
      }
    ];
    
    // 3. 处理异步函数
    const asyncPatterns = [
      // async function name()
      {
        pattern: /^(\s*async\s+function\s+[a-zA-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: Promise<any> {'
      },
      // const name = async () =>
      {
        pattern: /^(\s*const\s+[a-zA-Z][a-zA-Z0-9]*\s*=\s*async\s*\([^)]*\))\s*=>/gm,
        replacement: '$1: Promise<any> =>'
      }
    ];
    
    // 4. 处理 Hook 函数
    const hookPatterns = [
      // function useSomething()
      {
        pattern: /^(\s*function\s+use[A-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: any {'
      },
      // const useSomething = () =>
      {
        pattern: /^(\s*const\s+use[A-Z][a-zA-Z0-9]*\s*=\s*\([^)]*\))\s*=>/gm,
        replacement: '$1: any =>'
      },
      // export function useSomething()
      {
        pattern: /^(\s*export\s+function\s+use[A-Z][a-zA-Z0-9]*\s*\([^)]*\))\s*{/gm,
        replacement: '$1: any {'
      }
    ];
    
    // 应用所有模式
    const allPatterns = [...componentPatterns, ...handlerPatterns, ...asyncPatterns, ...hookPatterns];
    
    for (const { pattern, replacement } of allPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复返回类型: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始批量修复返回类型...\n');
  
  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];
  
  let totalFixed = 0;
  
  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;
    
    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          processDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          if (addReturnTypes(fullPath)) {
            totalFixed++;
          }
        }
      }
    }
    
    processDirectory(dir);
  }
  
  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的返回类型`);
  
  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    execSync('pnpm lint 2>&1 | grep -c "Missing return type"', { stdio: 'inherit' });
  } catch (error) {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { addReturnTypes };

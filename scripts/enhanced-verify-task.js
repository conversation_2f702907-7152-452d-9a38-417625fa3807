#!/usr/bin/env node

/**
 * 增强版 verify_task 工具 - 强制三层验证机制
 * 集成自动化检查、AI审查、人类确认三层验证
 * 只有全部通过才能完成任务验证
 */
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

// 加载配置
const configPath = path.join(process.cwd(), '.verify-task-config.json');
let config = {};

if (fs.existsSync(configPath)) {
  config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} else {
  console.warn('⚠️ verify_task 配置文件不存在，使用默认配置');
  config = {
    enhancedVerification: { enabled: true, enforceAllLayers: true },
    layerConfiguration: {
      layer1: { script: 'scripts/automated-checks.js', required: true },
      layer2: { script: 'scripts/ai-review.js', required: true },
      layer3: { script: 'scripts/human-verification.js', required: true },
    },
  };
}

/**
 * 检查紧急跳过权限
 */
function checkEmergencySkip() {
  if (!config.emergencySkip?.enabled) {
    return { allowed: false, reason: '紧急跳过功能未启用' };
  }

  // 检查环境变量
  const envVar =
    config.emergencySkip.environmentVariable || 'VERIFY_TASK_EMERGENCY_SKIP';
  if (process.env[envVar] === 'true') {
    return {
      allowed: true,
      method: 'environment_variable',
      reason: `环境变量 ${envVar} 已设置`,
    };
  }

  // 检查命令行参数
  if (process.argv.includes('--emergency-skip')) {
    return {
      allowed: true,
      method: 'command_line',
      reason: '命令行参数 --emergency-skip 已设置',
    };
  }

  return { allowed: false, reason: '未找到有效的紧急跳过权限' };
}

/**
 * 创建验证日志
 */
function createVerificationLog(taskId, summary, score) {
  const logDir = config.logging?.directory || '.verification-log';

  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const logFile = path.join(
    logDir,
    `enhanced-verify-${taskId}-${timestamp}.json`
  );

  const logData = {
    taskId,
    summary,
    score,
    startTime: new Date().toISOString(),
    layers: {},
    status: 'started',
    version: '1.0.0',
  };

  fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
  return logFile;
}

/**
 * 更新验证日志
 */
function updateVerificationLog(logFile, updates) {
  if (fs.existsSync(logFile)) {
    const logData = JSON.parse(fs.readFileSync(logFile, 'utf8'));
    Object.assign(logData, updates);
    logData.lastUpdated = new Date().toISOString();
    fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
  }
}

/**
 * 执行第一层：自动化检查
 */
async function executeLayer1(taskId, logFile) {
  console.log('🔄 执行第一层：自动化检查...');
  const startTime = Date.now();

  try {
    const layer1Config = config.layerConfiguration.layer1;
    const timeout = (layer1Config.timeout || 120) * 1000;

    // 执行自动化检查脚本
    const { stdout } = await execAsync(
      'node scripts/automated-checks.js --complexity',
      {
        timeout,
        cwd: process.cwd(),
      }
    );

    const endTime = Date.now();
    const result = {
      success: true,
      duration: endTime - startTime,
      output: stdout,
      error: null,
    };

    updateVerificationLog(logFile, {
      'layers.layer1': result,
    });

    console.log('✅ 第一层自动化检查通过');
    return result;
  } catch (error) {
    const endTime = Date.now();
    const result = {
      success: false,
      duration: endTime - startTime,
      output: null,
      error: error.message,
    };

    updateVerificationLog(logFile, {
      'layers.layer1': result,
    });

    console.error('❌ 第一层自动化检查失败:', error.message);
    throw new Error(`第一层自动化检查失败: ${error.message}`);
  }
}

/**
 * 执行第二层：AI审查
 */
async function executeLayer2(taskId, logFile) {
  console.log('🤖 执行第二层：AI审查（实用主义导向）...');
  const startTime = Date.now();

  try {
    const layer2Config = config.layerConfiguration.layer2;
    const timeout = (layer2Config.timeout || 60) * 1000;

    // 执行AI审查脚本
    const { stdout } = await execAsync('node scripts/ai-review.js', {
      timeout,
      cwd: process.cwd(),
    });

    const endTime = Date.now();

    // 解析AI审查结果
    let aiScore = 85; // 默认分数
    if (stdout.includes('综合评分:')) {
      const scoreMatch = stdout.match(/综合评分:\s*(\d+)/);
      if (scoreMatch) {
        aiScore = parseInt(scoreMatch[1]);
      }
    }

    const result = {
      success: aiScore >= 85,
      duration: endTime - startTime,
      score: aiScore,
      output: stdout,
      error: null,
    };

    updateVerificationLog(logFile, {
      'layers.layer2': result,
    });

    if (result.success) {
      console.log(`✅ 第二层AI审查通过 (评分: ${aiScore}/100)`);
    } else {
      console.error(`❌ 第二层AI审查未达标 (评分: ${aiScore}/100, 需要: ≥85)`);
      throw new Error(`AI审查评分不足: ${aiScore}/100`);
    }

    return result;
  } catch (error) {
    const endTime = Date.now();
    const result = {
      success: false,
      duration: endTime - startTime,
      score: 0,
      output: null,
      error: error.message,
    };

    updateVerificationLog(logFile, {
      'layers.layer2': result,
    });

    console.error('❌ 第二层AI审查失败:', error.message);
    throw new Error(`第二层AI审查失败: ${error.message}`);
  }
}

/**
 * 执行第三层：人类确认
 */
async function executeLayer3(taskId, logFile) {
  console.log('👤 执行第三层：人类确认...');
  const startTime = Date.now();

  try {
    const layer3Config = config.layerConfiguration.layer3;
    const timeout = (layer3Config.timeout || 600) * 1000;

    // 执行人类确认脚本
    const { stdout } = await execAsync(
      `node scripts/human-verification.js ${taskId}`,
      {
        timeout,
        cwd: process.cwd(),
        stdio: 'inherit',
      }
    );

    const endTime = Date.now();

    // 检查是否生成了验证令牌
    const hasToken =
      stdout.includes('验证令牌:') || stdout.includes('人类确认验证通过');

    const result = {
      success: hasToken,
      duration: endTime - startTime,
      output: stdout,
      error: null,
    };

    updateVerificationLog(logFile, {
      'layers.layer3': result,
    });

    if (result.success) {
      console.log('✅ 第三层人类确认通过');
    } else {
      console.error('❌ 第三层人类确认失败');
      throw new Error('人类确认验证失败');
    }

    return result;
  } catch (error) {
    const endTime = Date.now();
    const result = {
      success: false,
      duration: endTime - startTime,
      output: null,
      error: error.message,
    };

    updateVerificationLog(logFile, {
      'layers.layer3': result,
    });

    console.error('❌ 第三层人类确认失败:', error.message);
    throw new Error(`第三层人类确认失败: ${error.message}`);
  }
}

/**
 * 增强版任务验证主函数
 */
async function enhancedVerifyTask(taskId, summary, score) {
  console.log('🎯 开始强制三层审查验证...');
  console.log('📋 企业级质量保障体系 - 三层验证机制');
  console.log(`📝 任务ID: ${taskId}`);
  console.log(`📊 预期评分: ${score}/100`);

  const startTime = Date.now();

  // 检查紧急跳过
  const emergencySkip = checkEmergencySkip();
  if (emergencySkip.allowed) {
    console.log('⚠️ 检测到紧急跳过权限');
    console.log(`📋 跳过原因: ${emergencySkip.reason}`);
    console.log('🚨 警告: 跳过三层验证可能影响代码质量');

    // 记录紧急跳过
    const logFile = createVerificationLog(taskId, summary, score);
    updateVerificationLog(logFile, {
      emergencySkip: true,
      skipReason: emergencySkip.reason,
      skipMethod: emergencySkip.method,
      status: 'emergency_skipped',
      endTime: new Date().toISOString(),
    });

    return {
      success: true,
      skipped: true,
      reason: emergencySkip.reason,
      logFile,
    };
  }

  // 创建验证日志
  const logFile = createVerificationLog(taskId, summary, score);

  try {
    // 强制执行三层验证
    const layer1Result = await executeLayer1(taskId, logFile);
    const layer2Result = await executeLayer2(taskId, logFile);
    const layer3Result = await executeLayer3(taskId, logFile);

    // 计算综合结果
    const totalTime = Date.now() - startTime;
    const allLayersPass =
      layer1Result.success && layer2Result.success && layer3Result.success;

    // 更新最终日志
    updateVerificationLog(logFile, {
      status: allLayersPass ? 'completed' : 'failed',
      endTime: new Date().toISOString(),
      totalDuration: totalTime,
      finalScore: score,
      allLayersPass,
    });

    if (allLayersPass) {
      console.log('\n🎉 强制三层审查验证全部通过！');
      console.log('='.repeat(50));
      console.log(
        `✅ 第一层自动化检查: 通过 (${Math.round(layer1Result.duration / 1000)}秒)`
      );
      console.log(
        `✅ 第二层AI审查: 通过 (${Math.round(layer2Result.duration / 1000)}秒, 评分: ${layer2Result.score}/100)`
      );
      console.log(
        `✅ 第三层人类确认: 通过 (${Math.round(layer3Result.duration / 1000)}秒)`
      );
      console.log(`⏱️ 总耗时: ${Math.round(totalTime / 1000)}秒`);
      console.log(`📄 详细日志: ${logFile}`);

      return {
        success: true,
        taskId,
        summary,
        score,
        layers: {
          layer1: layer1Result,
          layer2: layer2Result,
          layer3: layer3Result,
        },
        totalTime,
        logFile,
      };
    } else {
      throw new Error('一个或多个验证层失败');
    }
  } catch (error) {
    updateVerificationLog(logFile, {
      status: 'failed',
      endTime: new Date().toISOString(),
      error: error.message,
    });

    console.error('\n❌ 强制三层审查验证失败');
    console.error('='.repeat(50));
    console.error(`💥 错误信息: ${error.message}`);
    console.error(`📄 详细日志: ${logFile}`);

    throw error;
  }
}

/**
 * 主执行函数
 */
async function main() {
  const taskId = process.argv[2] || 'demo-task';
  const summary = process.argv[3] || '演示任务完成';
  const score = parseInt(process.argv[4]) || 90;

  try {
    const result = await enhancedVerifyTask(taskId, summary, score);

    if (result.success) {
      console.log('\n✅ 任务验证成功完成！');
      process.exit(0);
    } else {
      console.error('\n❌ 任务验证失败！');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 任务验证执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 增强验证脚本执行失败:', error);
    process.exit(1);
  });
}

export { enhancedVerifyTask, executeLayer1, executeLayer2, executeLayer3 };

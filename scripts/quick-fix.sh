#!/bin/bash

# ESLint 快速批量修复脚本
# 使用 sed 和 find 命令批量修复常见的 ESLint 错误

echo "🚀 开始批量修复 ESLint 错误..."

# 计数器
console_fixes=0
boolean_fixes=0
nullish_fixes=0

# 1. 修复 console 语句 (63个错误)
echo "📝 修复 console 语句..."

find src -name "*.ts" -o -name "*.tsx" | while read file; do
    # 备份原文件
    cp "$file" "$file.bak"
    
    # 包装 console 语句
    sed -i '' 's/^[[:space:]]*console\.\(log\|warn\|error\|info\|debug\)(/  if (process.env.NODE_ENV === '\''development'\'') {\n    console.\1(/g' "$file"
    
    # 检查是否有修改
    if ! cmp -s "$file" "$file.bak"; then
        echo "  ✅ 修复了 $file 中的 console 语句"
        ((console_fixes++))
    fi
    
    # 删除备份文件
    rm "$file.bak"
done

echo "✅ 修复了 $console_fixes 个文件中的 console 语句"

# 2. 修复严格布尔表达式 (115个错误)
echo "🔍 修复严格布尔表达式..."

find src -name "*.ts" -o -name "*.tsx" | while read file; do
    cp "$file" "$file.bak"
    
    # 修复 nullable 对象检查: obj && obj.prop -> obj !== null && obj !== undefined && obj.prop
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) && \1\./\1 !== null \&\& \1 !== undefined \&\& \1./g' "$file"
    
    # 修复 nullable 字符串检查: str && str.length -> str !== null && str !== undefined && str.length > 0
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) && \1\.length/\1 !== null \&\& \1 !== undefined \&\& \1.length > 0/g' "$file"
    
    if ! cmp -s "$file" "$file.bak"; then
        echo "  ✅ 修复了 $file 中的严格布尔表达式"
        ((boolean_fixes++))
    fi
    
    rm "$file.bak"
done

echo "✅ 修复了 $boolean_fixes 个文件中的严格布尔表达式"

# 3. 修复 nullish coalescing (37个错误)
echo "🔄 修复 nullish coalescing..."

find src -name "*.ts" -o -name "*.tsx" | while read file; do
    cp "$file" "$file.bak"
    
    # 替换简单的 || 为 ?? (仅针对明显的默认值情况)
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || '\'''\''/'\'1' ?? '\'\''/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || ""/\1 ?? ""/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || 0/\1 ?? 0/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || false/\1 ?? false/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || true/\1 ?? true/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || \[\]/\1 ?? []/g' "$file"
    sed -i '' 's/\([a-zA-Z_][a-zA-Z0-9_]*\) || {}/\1 ?? {}/g' "$file"
    
    if ! cmp -s "$file" "$file.bak"; then
        echo "  ✅ 修复了 $file 中的 nullish coalescing"
        ((nullish_fixes++))
    fi
    
    rm "$file.bak"
done

echo "✅ 修复了 $nullish_fixes 个文件中的 nullish coalescing"

# 4. 运行 ESLint 自动修复
echo "🔧 运行 ESLint 自动修复..."
pnpm lint --fix 2>/dev/null || true

# 5. 检查剩余错误
echo "🔍 检查剩余错误数量..."
remaining_errors=$(pnpm lint 2>&1 | grep -c "Error:" || echo "0")

echo ""
echo "📊 修复结果统计:"
echo "  Console 语句修复: $console_fixes 个文件"
echo "  严格布尔表达式修复: $boolean_fixes 个文件"  
echo "  Nullish coalescing 修复: $nullish_fixes 个文件"
echo "  剩余错误数量: $remaining_errors"

if [ "$remaining_errors" -eq 0 ]; then
    echo "🎉 恭喜！所有 ESLint 错误已修复！"
else
    echo "📝 还需要手动修复 $remaining_errors 个错误"
    echo ""
    echo "🔍 剩余错误类型分析:"
    pnpm lint 2>&1 | grep "Error:" | sed 's/.*Error: //' | sort | uniq -c | sort -nr | head -10
fi

echo ""
echo "✅ 批量修复完成！"

#!/usr/bin/env node

/**
 * 自动化检查脚本 - 第一层验证
 * 基于 lefthook.yml 和 AI辅助质量体系文档的自动化检查逻辑
 */
import { execSync } from 'child_process';

// import fs from 'fs';
// import path from 'path';

// 加载质量配置（供未来扩展使用）
// const configPath = path.join(process.cwd(), '.quality-config.json');

// 配置加载函数（供未来扩展使用）
// function loadConfig() {
//   if (fs.existsSync(configPath)) {
//     return JSON.parse(fs.readFileSync(configPath, 'utf8'));
//   }
//   return {};
// }

/**
 * 执行命令并返回结果
 */
function executeCommand(command, description) {
  console.log(`🔍 执行 ${description}...`);
  try {
    const startTime = Date.now();
    execSync(command, { stdio: 'inherit' });
    const duration = Date.now() - startTime;
    console.log(`✅ ${description} 通过 (${duration}ms)`);
    return { success: true, duration, command };
  } catch (error) {
    console.error(`❌ ${description} 失败`);
    console.error(`命令: ${command}`);
    console.error(`错误: ${error.message}`);
    return { success: false, error: error.message, command };
  }
}

/**
 * 执行测试和覆盖率检查
 */
function executeTestChecks() {
  console.log('🧪 开始测试和覆盖率检查...');

  const testResult = executeCommand('pnpm test:run', '单元测试执行');
  if (!testResult.success) {
    return testResult;
  }

  const coverageResult = executeCommand('pnpm test:coverage', '测试覆盖率检查');
  return coverageResult;
}

/**
 * 执行安全检查
 */
function executeSecurityChecks() {
  console.log('🔒 开始安全检查...');

  // 依赖安全审计
  const auditResult = executeCommand('pnpm security:audit', '依赖安全审计');
  if (!auditResult.success) {
    console.warn('⚠️ 依赖安全审计发现问题，但继续执行其他检查');
  }

  // 安全扫描（如果脚本存在）
  try {
    const securityScanResult = executeCommand(
      'pnpm security:scan',
      '代码安全扫描'
    );
    return securityScanResult;
  } catch {
    console.warn('⚠️ 安全扫描脚本不存在，跳过此检查');
    return { success: true, skipped: true };
  }
}

/**
 * 执行性能和可访问性检查
 */
function executePerformanceChecks() {
  console.log('⚡ 开始性能和可访问性检查...');

  try {
    // Lighthouse CI 检查（如果配置存在）
    const lighthouseResult = executeCommand(
      'pnpm lhci:check',
      'Lighthouse 性能检查'
    );
    return lighthouseResult;
  } catch {
    console.warn('⚠️ Lighthouse CI 未配置，跳过性能检查');
    return { success: true, skipped: true };
  }
}

/**
 * 执行代码质量深度检查
 */
function executeDeepQualityChecks() {
  console.log('🔍 开始代码质量深度检查...');

  // 使用专用的深度检查配置文件（兼容 ESLint 9 Flat Config）
  const checks = [
    {
      command: 'npx eslint . --config eslint.deep.config.mjs --max-warnings 0',
      description: '深度质量检查（SonarJS + Unicorn + Security）',
      optional: true,
    },
  ];

  let allPassed = true;
  for (const check of checks) {
    try {
      const result = executeCommand(check.command, check.description);
      if (!result.success && !check.optional) {
        allPassed = false;
      }
    } catch (error) {
      if (!check.optional) {
        allPassed = false;
      }
      console.warn(`⚠️ ${check.description} 跳过: ${error.message}`);
    }
  }

  return { success: allPassed };
}

/**
 * 并行执行基础检查
 */
async function executeParallelChecks() {
  console.log('🚀 开始并行执行基础质量检查...');

  const parallelCommand = 'pnpm quality:parallel';
  return executeCommand(parallelCommand, '并行基础质量检查');
}

/**
 * 顺序执行检查
 */
async function executeSequentialChecks() {
  console.log('📋 开始顺序执行完整质量检查...');

  const checks = [
    // 第一阶段：基础检查（关键）
    {
      command: 'pnpm type-check',
      description: 'TypeScript 类型检查',
      critical: true,
      stage: '基础',
    },
    {
      command: 'pnpm lint:check',
      description: 'ESLint 代码规范检查',
      critical: true,
      stage: '基础',
    },
    {
      command: 'pnpm format:check',
      description: 'Prettier 格式检查',
      critical: true,
      stage: '基础',
    },

    // 第二阶段：测试检查（重要）
    {
      fn: () => executeTestChecks(),
      description: '测试和覆盖率检查',
      critical: false,
      stage: '测试',
    },

    // 第三阶段：安全检查（重要）
    {
      fn: () => executeSecurityChecks(),
      description: '安全检查',
      critical: false,
      stage: '安全',
    },

    // 第四阶段：深度质量检查（可选）
    {
      fn: () => executeDeepQualityChecks(),
      description: '深度质量检查',
      critical: false,
      stage: '深度质量',
    },

    // 第五阶段：性能检查（可选）
    {
      fn: () => executePerformanceChecks(),
      description: '性能检查',
      critical: false,
      stage: '性能',
    },
  ];

  const results = [];
  let currentStage = '';

  for (const check of checks) {
    if (check.stage !== currentStage) {
      currentStage = check.stage;
      console.log(`\n🔄 进入 ${currentStage} 检查阶段...`);
    }

    let result;
    if (check.fn) {
      result = check.fn();
    } else {
      result = executeCommand(check.command, check.description);
    }

    results.push(result);

    if (!result.success && check.critical) {
      console.error(`❌ 关键检查失败，停止后续检查`);
      return { success: false, results, failedAt: check.description };
    } else if (!result.success) {
      console.warn(`⚠️ 非关键检查失败，继续执行后续检查`);
    }
  }

  return { success: true, results };
}

/**
 * 执行复杂度检查
 */
async function executeComplexityCheck() {
  console.log('🔍 开始代码复杂度检查...');
  return executeCommand('pnpm lint:check', '代码复杂度和质量检查');
}

/**
 * 执行构建检查
 */
async function executeBuildCheck() {
  console.log('🏗️ 开始构建验证...');
  return executeCommand('pnpm build', 'Next.js 构建验证');
}

/**
 * 主执行函数
 */
async function main() {
  console.log('🎯 开始第一层自动化检查...');
  console.log('📋 基于 lefthook.yml 和 AI辅助质量体系文档标准');

  const startTime = Date.now();
  const mode = process.argv.includes('--parallel') ? 'parallel' : 'sequential';
  const includeBuild = process.argv.includes('--build');
  const includeComplexity = process.argv.includes('--complexity');
  const includeComplete = process.argv.includes('--complete');
  const includeSecurity = process.argv.includes('--security');
  const includeDeep = process.argv.includes('--deep');

  let checkResult;

  // 根据参数选择检查模式
  if (includeComplete) {
    console.log('🔍 执行完整质量检查（包含所有工具和插件）...');
    checkResult = await executeSequentialChecks();
  } else if (includeSecurity) {
    console.log('🔒 执行安全专项检查...');
    checkResult = await executeSecurityChecks();
  } else if (includeDeep) {
    console.log('🔍 执行深度质量检查...');
    checkResult = await executeDeepQualityChecks();
  } else if (mode === 'parallel') {
    checkResult = await executeParallelChecks();
  } else {
    checkResult = await executeSequentialChecks();
  }

  if (!checkResult.success) {
    console.error('❌ 质量检查失败');
    process.exit(1);
  }

  // 可选的复杂度检查
  if (includeComplexity) {
    const complexityResult = await executeComplexityCheck();
    if (!complexityResult.success) {
      console.error('❌ 代码复杂度检查失败');
      process.exit(1);
    }
  }

  // 可选的构建检查
  if (includeBuild) {
    const buildResult = await executeBuildCheck();
    if (!buildResult.success) {
      console.error('❌ 构建验证失败');
      process.exit(1);
    }
  }

  const totalTime = Date.now() - startTime;
  console.log(`✅ 第一层自动化检查全部通过！总耗时: ${totalTime}ms`);

  return {
    success: true,
    totalTime,
    mode,
    includeBuild,
    includeComplexity,
    timestamp: new Date().toISOString(),
  };
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 自动化检查执行失败:', error);
    process.exit(1);
  });
}

export {
  main,
  executeSequentialChecks,
  executeParallelChecks,
  executeComplexityCheck,
  executeBuildCheck,
};

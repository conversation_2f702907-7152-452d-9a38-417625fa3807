#!/usr/bin/env node

/**
 * 自定义安全检查脚本
 * 企业级安全审计工具 - 检查代码中的安全漏洞和最佳实践
 */
import { execSync } from 'child_process';
import { readFileSync, readdirSync, statSync } from 'fs';
import { dirname, extname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 安全检查配置
const SECURITY_CONFIG = {
  // 敏感信息模式
  sensitivePatterns: [
    /password\s*[:=]\s*['"][^'"]+['"]/gi,
    /secret\s*[:=]\s*['"][^'"]+['"]/gi,
    /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
    /token\s*[:=]\s*['"][^'"]+['"]/gi,
    /private[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
    /access[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi,
    /database[_-]?url\s*[:=]\s*['"][^'"]+['"]/gi,
    /mongodb\+srv:\/\/[^'"]+/gi,
    /postgres:\/\/[^'"]+/gi,
    /mysql:\/\/[^'"]+/gi,
  ],

  // 不安全的函数调用
  unsafeFunctions: [
    /eval\s*\(/gi,
    /Function\s*\(/gi,
    /setTimeout\s*\(\s*['"][^'"]*['"]/gi,
    /setInterval\s*\(\s*['"][^'"]*['"]/gi,
    /document\.write\s*\(/gi,
    /innerHTML\s*=/gi,
    /outerHTML\s*=/gi,
    /insertAdjacentHTML\s*\(/gi,
  ],

  // 不安全的导入
  unsafeImports: [
    /require\s*\(\s*[^'"]*\$\{[^}]+\}/gi,
    /import\s*\(\s*[^'"]*\$\{[^}]+\}/gi,
    /require\s*\(\s*process\.env\./gi,
  ],

  // 文件扩展名白名单
  allowedExtensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.md'],

  // 忽略的目录
  ignoredDirs: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'out',
    '.pnpm-store',
    '.lighthouseci',
    'performance-reports',
  ],

  // 忽略的文件
  ignoredFiles: ['.env.example', '.env.template', 'README.md', 'CHANGELOG.md'],
};

/**
 * 安全检查结果统计
 */
class SecurityReport {
  constructor() {
    this.issues = [];
    this.filesScanned = 0;
    this.startTime = Date.now();
  }

  addIssue(type, severity, file, line, description, code = '') {
    this.issues.push({
      type,
      severity,
      file,
      line,
      description,
      code: code.trim(),
      timestamp: new Date().toISOString(),
    });
  }

  getSummary() {
    const critical = this.issues.filter(
      (i) => i.severity === 'critical'
    ).length;
    const high = this.issues.filter((i) => i.severity === 'high').length;
    const medium = this.issues.filter((i) => i.severity === 'medium').length;
    const low = this.issues.filter((i) => i.severity === 'low').length;

    return {
      total: this.issues.length,
      critical,
      high,
      medium,
      low,
      filesScanned: this.filesScanned,
      duration: Date.now() - this.startTime,
    };
  }

  hasHighRiskIssues() {
    return this.issues.some(
      (i) => i.severity === 'critical' || i.severity === 'high'
    );
  }
}

/**
 * 递归扫描目录中的文件
 */
function scanDirectory(dir, report) {
  const items = readdirSync(dir);

  for (const item of items) {
    const fullPath = join(dir, item);
    const stat = statSync(fullPath);

    if (stat.isDirectory()) {
      if (!SECURITY_CONFIG.ignoredDirs.includes(item)) {
        scanDirectory(fullPath, report);
      }
    } else if (stat.isFile()) {
      const ext = extname(item);
      if (
        SECURITY_CONFIG.allowedExtensions.includes(ext) &&
        !SECURITY_CONFIG.ignoredFiles.includes(item)
      ) {
        scanFile(fullPath, report);
      }
    }
  }
}

/**
 * 扫描单个文件
 */
function scanFile(filePath, report) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const relativePath = filePath.replace(projectRoot + '/', '');

    report.filesScanned++;

    // 检查敏感信息
    checkSensitiveData(content, lines, relativePath, report);

    // 检查不安全的函数
    checkUnsafeFunctions(content, lines, relativePath, report);

    // 检查不安全的导入
    checkUnsafeImports(content, lines, relativePath, report);

    // 检查文件权限（仅限配置文件）
    checkFilePermissions(filePath, relativePath, report);
  } catch (error) {
    report.addIssue(
      'scan-error',
      'medium',
      filePath.replace(projectRoot + '/', ''),
      0,
      `文件扫描失败: ${error.message}`
    );
  }
}

/**
 * 检查敏感信息
 */
function checkSensitiveData(content, lines, filePath, report) {
  SECURITY_CONFIG.sensitivePatterns.forEach((pattern) => {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const line = lines[lineNumber - 1];

      // 跳过注释和示例
      if (
        line.trim().startsWith('//') ||
        line.trim().startsWith('*') ||
        line.trim().startsWith('#') ||
        line.includes('example') ||
        line.includes('placeholder') ||
        line.includes('your_') ||
        line.includes('YOUR_') ||
        line.includes('❌') ||
        line.includes('错误示例') ||
        filePath.includes('docs/') ||
        filePath.includes('README')
      ) {
        continue;
      }

      report.addIssue(
        'sensitive-data',
        'critical',
        filePath,
        lineNumber,
        '检测到可能的敏感信息泄露',
        line
      );
    }
  });
}

/**
 * 检查不安全的函数调用
 */
function checkUnsafeFunctions(content, lines, filePath, report) {
  SECURITY_CONFIG.unsafeFunctions.forEach((pattern) => {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const line = lines[lineNumber - 1];

      // 跳过文档中的示例代码
      if (
        line.includes('❌') ||
        line.includes('错误示例') ||
        filePath.includes('docs/') ||
        filePath.includes('README')
      ) {
        continue;
      }

      report.addIssue(
        'unsafe-function',
        'high',
        filePath,
        lineNumber,
        '使用了不安全的函数调用',
        line
      );
    }
  });
}

/**
 * 检查不安全的导入
 */
function checkUnsafeImports(content, lines, filePath, report) {
  SECURITY_CONFIG.unsafeImports.forEach((pattern) => {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const line = lines[lineNumber - 1];

      // 跳过文档中的示例代码
      if (
        line.includes('❌') ||
        line.includes('错误示例') ||
        filePath.includes('docs/') ||
        filePath.includes('README')
      ) {
        continue;
      }

      report.addIssue(
        'unsafe-import',
        'high',
        filePath,
        lineNumber,
        '动态导入可能存在安全风险',
        line
      );
    }
  });
}

/**
 * 检查文件权限
 */
function checkFilePermissions(filePath, relativePath, report) {
  try {
    const stat = statSync(filePath);
    const mode = stat.mode;

    // 检查是否有其他用户的写权限
    if (mode & 0o002) {
      report.addIssue(
        'file-permissions',
        'medium',
        relativePath,
        0,
        '文件对其他用户可写，存在安全风险'
      );
    }
  } catch {
    // 忽略权限检查错误
  }
}

/**
 * 运行ESLint安全检查
 */
function runESLintSecurityCheck(report) {
  try {
    console.log('🔍 运行 ESLint 安全规则检查...');
    execSync('pnpm lint:check', {
      cwd: projectRoot,
      stdio: 'pipe',
      encoding: 'utf8',
    });
    console.log('✅ ESLint 安全检查通过');
  } catch (error) {
    const output = error.stdout || error.stderr || '';
    const securityIssues = output
      .split('\n')
      .filter(
        (line) => line.includes('security/') || line.includes('Security')
      );

    if (securityIssues.length > 0) {
      report.addIssue(
        'eslint-security',
        'high',
        'multiple-files',
        0,
        `ESLint 安全检查发现 ${securityIssues.length} 个问题`,
        securityIssues.slice(0, 5).join('\n')
      );
    }
  }
}

/**
 * 打印安全报告
 */
function printReport(report) {
  const summary = report.getSummary();

  console.log('\n📊 安全检查报告');
  console.log('='.repeat(50));
  console.log(`扫描文件数: ${summary.filesScanned}`);
  console.log(`扫描耗时: ${summary.duration}ms`);
  console.log(`发现问题: ${summary.total}`);
  console.log(`  - 严重: ${summary.critical}`);
  console.log(`  - 高危: ${summary.high}`);
  console.log(`  - 中危: ${summary.medium}`);
  console.log(`  - 低危: ${summary.low}`);

  if (report.issues.length > 0) {
    console.log('\n🚨 发现的安全问题:');
    console.log('-'.repeat(50));

    report.issues.forEach((issue, index) => {
      console.log(
        `\n${index + 1}. [${issue.severity.toUpperCase()}] ${issue.type}`
      );
      console.log(`   文件: ${issue.file}:${issue.line}`);
      console.log(`   描述: ${issue.description}`);
      if (issue.code) {
        console.log(`   代码: ${issue.code}`);
      }
    });
  }

  console.log('\n' + '='.repeat(50));

  if (report.hasHighRiskIssues()) {
    console.log('❌ 安全检查失败: 发现高风险安全问题');
    process.exit(1);
  } else if (summary.total > 0) {
    console.log('⚠️  安全检查完成: 发现中低风险问题，建议修复');
    process.exit(0);
  } else {
    console.log('✅ 安全检查通过: 未发现安全问题');
    process.exit(0);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔒 开始安全检查...');
  console.log(`📁 扫描目录: ${projectRoot}`);

  const report = new SecurityReport();

  // 运行ESLint安全检查
  runESLintSecurityCheck(report);

  // 扫描项目文件
  scanDirectory(projectRoot, report);

  // 打印报告
  printReport(report);
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { SecurityReport, scanDirectory, scanFile };

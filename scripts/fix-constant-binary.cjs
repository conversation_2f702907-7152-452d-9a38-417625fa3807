#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 修复常量二进制表达式错误的脚本
 */

function fixConstantBinary(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. 修复重复的空值检查模式
    const patterns = [
      // 修复类似 "!== undefined !== null !== null" 的模式
      {
        pattern:
          /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*!==\s*undefined\s*!==\s*null\s*!==\s*null/g,
        replacement: '$1 !== null && $1 !== undefined',
      },

      // 修复类似 "null !== undefined && null !== ''" 的模式
      {
        pattern: /null\s*!==\s*undefined\s*&&\s*null\s*!==\s*''/g,
        replacement: 'true', // 这些总是 true
      },

      // 修复类似 "undefined !== undefined" 的模式
      {
        pattern: /undefined\s*!==\s*undefined/g,
        replacement: 'false', // 这总是 false
      },

      // 修复类似 "null !== null" 的模式
      {
        pattern: /null\s*!==\s*null/g,
        replacement: 'false', // 这总是 false
      },

      // 修复复杂的重复模式
      {
        pattern:
          /\(\(\(([a-zA-Z_$][a-zA-Z0-9_$]*)\s*!==\s*undefined\)\s*!==\s*null\)\s*!==\s*null/g,
        replacement: '$1 !== null && $1 !== undefined',
      },

      // 修复多重 && false 的模式
      {
        pattern: /\s*&&\s*false\s*&&/g,
        replacement: ' && false &&',
      },

      // 修复多重 && true 的模式
      {
        pattern: /\s*&&\s*true\s*&&/g,
        replacement: ' &&',
      },
    ];

    // 应用修复模式
    for (const { pattern, replacement } of patterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // 2. 清理简化后的表达式
    const cleanupPatterns = [
      // 移除多余的 && true
      {
        pattern: /\s*&&\s*true\s*&&/g,
        replacement: ' &&',
      },

      // 移除开头的 true &&
      {
        pattern: /true\s*&&\s*/g,
        replacement: '',
      },

      // 移除结尾的 && true
      {
        pattern: /\s*&&\s*true\s*$/gm,
        replacement: '',
      },

      // 简化 false && 任何东西 为 false
      {
        pattern: /false\s*&&\s*[^&)]+/g,
        replacement: 'false',
      },
    ];

    for (const { pattern, replacement } of cleanupPatterns) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // 3. 特殊处理：修复常见的条件检查模式
    const specificFixes = [
      // 修复 className 检查
      {
        pattern:
          /className\s*!==\s*null\s*&&\s*className\s*!==\s*undefined\s*&&\s*className\.length\s*>\s*0/g,
        replacement: 'className && className.length > 0',
      },

      // 修复 description 检查
      {
        pattern:
          /description\s*!==\s*null\s*&&\s*description\s*!==\s*undefined\s*&&\s*description\.length\s*>\s*0/g,
        replacement: 'description && description.length > 0',
      },

      // 修复简单的变量检查
      {
        pattern:
          /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*!==\s*null\s*&&\s*\1\s*!==\s*undefined/g,
        replacement: '$1 != null', // 使用 != null 来同时检查 null 和 undefined
      },
    ];

    for (const { pattern, replacement } of specificFixes) {
      const newContent = content.replace(pattern, replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 修复常量二进制表达式: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 开始修复常量二进制表达式...\n');

  // 获取所有需要修复的文件
  const extensions = ['.ts', '.tsx'];
  const directories = ['src/components', 'src/hooks', 'src/app', 'src/lib'];

  let totalFixed = 0;

  for (const dir of directories) {
    if (!fs.existsSync(dir)) continue;

    function processDirectory(dirPath) {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith('.') &&
          item !== 'node_modules'
        ) {
          processDirectory(fullPath);
        } else if (
          stat.isFile() &&
          extensions.some((ext) => item.endsWith(ext))
        ) {
          if (fixConstantBinary(fullPath)) {
            totalFixed++;
          }
        }
      }
    }

    processDirectory(dir);
  }

  console.log(`\n✅ 完成！共修复了 ${totalFixed} 个文件的常量二进制表达式`);

  // 运行 ESLint 检查结果
  console.log('\n🔍 检查修复结果...');
  try {
    const result = execSync(
      'pnpm lint 2>&1 | grep -c "constant binary expression"',
      { encoding: 'utf8' }
    );
    console.log(`剩余常量二进制表达式错误: ${result.trim()}`);
  } catch {
    console.log('检查完成');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixConstantBinary };

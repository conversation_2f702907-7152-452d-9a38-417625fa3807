#!/usr/bin/env node

/**
 * 更新所有任务的验证机制为强制三层审查格式
 * 统一验证标准，确保每个任务都使用强制三层审查
 */
import fs from 'fs';
import path from 'path';

/**
 * 强制三层审查验证标准模板
 */
function generateThreeLayerVerificationCriteria(
  taskName,
  specificCriteria = []
) {
  const baseTemplate = `**强制三层审查验证标准：**

**第一层：自动化检查**
1. 运行 \`pnpm quality:complete\` 完整质量检查通过（包含所有工具和插件）
2. 运行 \`pnpm quality:check\` 基础检查通过（类型检查、代码规范、格式检查）
3. 运行 \`pnpm test:run\` 单元测试全部通过
4. 运行 \`pnpm security:audit\` 依赖安全审计无高危漏洞
5. 运行 \`pnpm build\` 构建成功，无错误和警告

**第二层：AI审查（实用主义导向）**
6. 运行 \`pnpm ai:review\` 生成AI审查报告，综合评分 ≥85分
7. 运行 \`pnpm ai:security\` AI安全审查通过，无高危安全风险
8. AI审查报告体现实用主义导向，避免过度工程化建议
9. 代码质量评估符合企业级标准，可维护性良好

**第三层：人类确认**
10. 运行 \`pnpm verify:human\` 生成标准化确认清单
11. 完成所有必需确认项，生成验证令牌和时间戳记录
12. 浏览器验证功能正常，界面交互符合预期
13. 性能指标符合预期，用户体验良好

**任务特定验证：**`;

  // 添加任务特定的验证标准
  let taskSpecificSection = '';
  if (specificCriteria.length > 0) {
    taskSpecificSection = specificCriteria
      .map((criteria, index) => `${14 + index}. ${criteria}`)
      .join('\n');
  } else {
    // 根据任务名称生成默认的特定验证标准
    if (taskName.includes('组件')) {
      taskSpecificSection = `14. 组件在不同屏幕尺寸下显示正常
15. 组件交互功能正常工作
16. 组件样式符合设计规范`;
    } else if (taskName.includes('页面')) {
      taskSpecificSection = `14. 页面在浏览器中正常显示
15. 页面路由和导航功能正常
16. 页面内容和布局符合预期`;
    } else if (taskName.includes('配置') || taskName.includes('系统')) {
      taskSpecificSection = `14. 配置文件正确创建并包含预期内容
15. 系统功能按配置正常工作
16. 相关依赖和集成正常运行`;
    } else {
      taskSpecificSection = `14. 功能实现符合需求规格
15. 相关文件正确创建和修改
16. 整体功能测试通过`;
    }
  }

  return `${baseTemplate}\n${taskSpecificSection}

**验证完成标准：**
- 所有三层验证都必须通过
- 任务特定验证全部满足
- 生成完整的验证记录和日志`;
}

/**
 * 更新任务文件中的验证标准
 */
function updateTasksVerificationCriteria() {
  const tasksFilePath = path.join(process.cwd(), 'docs/date/tasks.json');

  if (!fs.existsSync(tasksFilePath)) {
    console.error('❌ 任务文件不存在:', tasksFilePath);
    return false;
  }

  try {
    // 读取任务文件
    const tasksData = JSON.parse(fs.readFileSync(tasksFilePath, 'utf8'));

    let updatedCount = 0;
    const excludeTaskIds = [
      '2ae1b482-183b-4f20-b117-e6547640bb61', // 强制三层审查 - 自动化检查脚本增强
      '09500c49-84e9-44d5-987c-ba155eb301ac', // 强制三层审查 - AI审查工具集成
      'd13a10fa-58d8-4dfe-98ea-7f59202080b1', // 强制三层审查 - 人类确认机制实现
      '9e1a82ef-3dc1-40b7-a873-2873e83a8701', // 强制三层审查 - verify_task 工具集成改造
    ];

    // 更新每个任务的验证标准
    tasksData.tasks.forEach((task) => {
      // 跳过强制三层审查任务本身
      if (excludeTaskIds.includes(task.id)) {
        return;
      }

      // 跳过已经有强制三层审查验证标准的任务
      if (
        task.verificationCriteria &&
        task.verificationCriteria.includes('强制三层审查验证标准')
      ) {
        return;
      }

      // 保存原有的验证标准作为任务特定验证
      const originalCriteria = task.verificationCriteria || '';
      const specificCriteria = originalCriteria
        .split('\n')
        .filter((line) => line.trim())
        .map((line) => line.replace(/^\d+\.\s*/, '').trim())
        .filter((line) => line.length > 0);

      // 生成新的强制三层审查验证标准
      task.verificationCriteria = generateThreeLayerVerificationCriteria(
        task.name,
        specificCriteria.slice(0, 3) // 最多保留3个原有标准
      );

      updatedCount++;
    });

    // 保存更新后的任务文件
    fs.writeFileSync(tasksFilePath, JSON.stringify(tasksData, null, 2));

    console.log(`✅ 成功更新 ${updatedCount} 个任务的验证标准`);
    console.log('📋 所有任务现在都使用强制三层审查验证机制');

    return true;
  } catch (error) {
    console.error('❌ 更新任务验证标准失败:', error.message);
    return false;
  }
}

/**
 * 验证更新结果
 */
function validateUpdate() {
  const tasksFilePath = path.join(process.cwd(), 'docs/date/tasks.json');

  try {
    const tasksData = JSON.parse(fs.readFileSync(tasksFilePath, 'utf8'));

    let threeLayerCount = 0;
    let totalTasks = tasksData.tasks.length;

    tasksData.tasks.forEach((task) => {
      if (
        task.verificationCriteria &&
        task.verificationCriteria.includes('强制三层审查验证标准')
      ) {
        threeLayerCount++;
      }
    });

    console.log('\n📊 验证更新结果：');
    console.log(`📝 总任务数: ${totalTasks}`);
    console.log(`✅ 使用强制三层审查的任务: ${threeLayerCount}`);
    console.log(
      `📈 覆盖率: ${Math.round((threeLayerCount / totalTasks) * 100)}%`
    );

    if (threeLayerCount === totalTasks) {
      console.log('🎉 所有任务都已成功应用强制三层审查验证机制！');
    } else {
      console.log(
        `⚠️ 还有 ${totalTasks - threeLayerCount} 个任务未应用强制三层审查`
      );
    }
  } catch (error) {
    console.error('❌ 验证更新结果失败:', error.message);
  }
}

/**
 * 主执行函数
 */
async function main() {
  console.log('🎯 开始更新所有任务的验证机制为强制三层审查格式...');
  console.log('📋 统一验证标准，确保企业级质量保障体系');

  const success = updateTasksVerificationCriteria();

  if (success) {
    validateUpdate();
    console.log('\n✅ 强制三层审查验证机制更新完成！');
    console.log('📋 现在所有任务都将使用统一的三层验证标准：');
    console.log('   第一层：自动化检查（质量、复杂度、构建）');
    console.log('   第二层：AI审查（实用主义导向，≥85分）');
    console.log('   第三层：人类确认（标准化清单，浏览器验证）');
  } else {
    console.error('❌ 更新失败，请检查错误信息');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('💥 更新脚本执行失败:', error);
    process.exit(1);
  });
}

export {
  updateTasksVerificationCriteria,
  generateThreeLayerVerificationCriteria,
  validateUpdate,
};

---
type: auto
description: "Project guidelines, technology stack, and development workflow"
---

# Project Guidelines

## Project Overview

**Enterprise-grade modern web application** built with the latest technology stack, implementing strict code quality standards and zero-tolerance error policy.

### Core Features
- **Enterprise Quality Standards**: 164 ESLint rules + 15 TypeScript strict configurations
- **Zero Warning Policy**: All quality checks must pass without warnings
- **Three-Layer Security Audit**: Automated checks + AI review + Human confirmation
- **Enterprise Performance Monitoring**: Lighthouse CI + Web Vitals + Real-time monitoring
- **Modern Technology Stack**: Latest versions of mainstream technologies
- **Automated Workflows**: Complete Git hooks, CI/CD and quality check processes
- **High Test Coverage**: Requires 80%+ test coverage

### Development Goals
1. **Code Quality**: Ensure readability, maintainability, and scalability
2. **Performance Optimization**: Enterprise performance standards (Lighthouse ≥90, INP ≤200ms)
3. **Security Protection**: Zero vulnerability policy + Three-layer security audit system
4. **Team Collaboration**: Standardized development processes and code standards
5. **Continuous Monitoring**: Real-time performance monitoring + Automated security scanning

## Technology Stack

### Core Framework Stack
- **Next.js 15** - App Router architecture, Server Components priority
- **React 19** - Concurrent features, enhanced Suspense
- **TypeScript 5** - Strict mode enabled for type safety

### Styling & UI System
- **Tailwind CSS 4.x** - CSS-first mode, high-performance engine
- **shadcn/ui** - Primary component library, design system consistency

### Quality & Development Tools
- **ESLint 9.x** - Flat config, 164 enterprise-grade rules + security rules
- **Prettier** - Unified code formatting standards
- **Vitest** - Modern testing framework, 80% coverage requirement
- **Lefthook** - Git hooks automation
- **Security Audit** - Custom security scanning + dependency audit
- **Performance Monitor** - Lighthouse CI + Web Vitals monitoring

### Build & Package Management
- **Turbopack** - High-performance build tool for development
- **PostCSS** - CSS processing and optimization
- **pnpm** - Efficient package manager

### Turbopack Development Standards

#### Development Workflow
- **Development Mode**: Use `pnpm dev` (Turbopack enabled by default)
- **Fast Reload**: Leverage Turbopack's 5-10x faster hot reload
- **Debugging**: Use `next dev --turbopack --show-all` for detailed information
- **Build Strategy**: Development with Turbopack, production with Webpack 5

#### Best Practices
- **Module Resolution**: Prefer ES modules for better tree shaking
- **Import Optimization**: Use static imports when possible
- **Cache Utilization**: Leverage Turbopack's incremental compilation
- **Performance**: Monitor development build times and optimize imports

## Development Workflow

### Quick Start
1. **Environment Setup**: Ensure Node.js 18+ and pnpm are installed
2. **Install Dependencies**: Run `pnpm install`
3. **Start Development**: Run `pnpm dev`
4. **Quality Check**: Run `pnpm quality:check`

### Development Process
1. **Branch Creation** - Create feature branch from main
2. **Code Development** - Follow coding standards and guidelines
3. **Quality Validation** - Run `pnpm quality:check` (must pass)
4. **Security Check** - Run `pnpm security:check` (zero vulnerability policy)
5. **Performance Test** - Run `pnpm perf:test` (enterprise standards)
6. **Code Commit** - Use conventional commit message format
7. **Peer Review** - Submit Pull Request for code review
8. **Integration** - Merge to main after approval

### Git Workflow Standards

#### Branch Strategy
- **main**: Production-ready code
- **feature/***: New feature development
- **bugfix/***: Bug fixes
- **hotfix/***: Emergency fixes

#### Commit Message Format (Conventional Commits)
```
<type>(<scope>): <subject>

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation updates
- style: Code formatting
- refactor: Code refactoring
- test: Test-related changes
- chore: Build tools or auxiliary tool changes

Examples:
- feat(auth): add user login functionality
- fix(ui): fix button style issue
- docs: update README documentation
```

### Git Hooks Automation

#### Pre-commit Checks
- TypeScript type checking (highest priority)
- ESLint code standard checking (includes security rules)
- Prettier formatting checking
- File size checking (prevent large files)
- Sensitive information checking (custom security scanning)
- Dependency security audit

#### Commit Message Validation
- Conventional Commits format validation
- Subject line length limits
- Required scope for certain types

#### Pre-push Checks
- Build testing (ensure code builds successfully)
- Final quality checking
- Test coverage validation

### Code Review Standards

#### Pull Request Checklist
- [ ] All CI checks pass
- [ ] Code coverage reaches 80%
- [ ] No TypeScript errors
- [ ] No ESLint warnings or errors
- [ ] Code format complies with Prettier standards
- [ ] Code logic is clear and understandable
- [ ] Functions and components have single responsibility
- [ ] Appropriate error handling
- [ ] Performance considerations
- [ ] Security considerations

#### Review Process
```
Developer creates PR
       ↓
Automated checks (CI/CD)
├── Type checking
├── Code standard checking
├── Test execution
├── Build verification
└── Security scanning
       ↓
Code review (Manual)
├── Code quality assessment
├── Architecture design review
├── Performance impact assessment
└── Security review
       ↓
Review approval → Merge to main branch
Review rejection → Modify and re-review
```

## Performance Requirements

### Core Web Vitals Enterprise Standards (2024 Latest)
- **LCP (Largest Contentful Paint)**: ≤ 2.5 seconds
- **INP (Interaction to Next Paint)**: ≤ 200 milliseconds (replaces FID)
- **CLS (Cumulative Layout Shift)**: ≤ 0.1
- **FCP (First Contentful Paint)**: ≤ 1.8 seconds
- **TTFB (Time to First Byte)**: ≤ 800 milliseconds

### Lighthouse Enterprise Score Requirements
- **Performance**: ≥ 90/100
- **Accessibility**: ≥ 95/100
- **Best Practices**: ≥ 90/100
- **SEO**: ≥ 90/100
- **PWA**: ≥ 80/100

**Note**: Detailed performance monitoring setup and tools are documented in `security-performance-standards.md`.

### Optimization Strategies
- Use Next.js Image component for image optimization
- Implement code splitting and lazy loading
- Optimize bundle size with proper tree shaking
- Use appropriate caching strategies
- Minimize JavaScript bundle size
- Optimize CSS delivery

### Monitoring and Analytics
- **Real-time Web Vitals monitoring** - Real-time performance metrics collection
- **Lighthouse CI integration** - Automated performance testing
- **Performance budgets** - Bundle size limits (initial ≤200KB, total ≤1MB)
- **Performance profiling tools** - Performance analysis and optimization
- **User experience metrics** - User experience metrics tracking
- **Bundle size monitoring** - Bundle size change monitoring

## Environment Configuration

### Development Environment
```bash
# Development server
pnpm dev                    # Start development server
pnpm dev --turbopack       # Use Turbopack (faster)
pnpm dev --port 3001       # Specify port

# Build and preview
pnpm build                 # Production build
pnpm start                 # Start production server
```

### Quality Assurance
```bash
# Core quality checks
pnpm quality:check         # Complete code quality check
pnpm quality:parallel      # Parallel quality check (faster)

# Individual checks
pnpm type-check            # TypeScript type checking
pnpm lint:check            # ESLint checking
pnpm format:check          # Prettier format checking
pnpm test:coverage         # Test coverage checking

# Build verification
pnpm build                 # Production build
pnpm start                 # Start production server
```

**Note**: Security and performance check commands are documented in `security-performance-standards.md`.

### Configuration Files
- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `components.json` - shadcn/ui component configuration
- `.env.local` - Local environment variables
- `.env.example` - Environment variables template

## Team Collaboration Standards

### Code Style
- Strictly follow Prettier and ESLint configurations
- Use consistent naming conventions
- Implement proper TypeScript types
- Write self-documenting code

### Documentation Requirements
- Update documentation for API changes
- Provide usage examples for new components
- Maintain README for project setup
- Document complex business logic

### Knowledge Sharing
- Conduct regular code review sessions
- Share best practices and learnings
- Maintain team coding standards
- Provide mentoring for new team members

## Continuous Improvement

### Regular Reviews
- Monthly development process reviews
- Identify improvement opportunities
- Update tools and dependencies regularly
- Optimize CI/CD processes continuously

### Quality Metrics
- Monitor code quality trends
- Track test coverage improvements
- Measure performance optimizations
- Assess security posture regularly

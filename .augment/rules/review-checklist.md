---
type: manual
description: "Code review checklist and troubleshooting guide"
---

# Review Checklist

## Code Review Checklist

### 📋 Essential Quality Checks
- [ ] **CI Pipeline** - All automated checks pass
- [ ] **Test Coverage** - Minimum 80% coverage achieved
- [ ] **Type Safety** - No TypeScript errors
- [ ] **Code Standards** - No ESLint warnings/errors (includes security rules)
- [ ] **Formatting** - Prettier standards complied
- [ ] **Security Audit** - Security checks pass (`pnpm security:check`)
- [ ] **Performance Test** - Performance tests pass (`pnpm perf:test`)

### 🔍 Code Quality Assessment
- [ ] **Clarity** - Logic is clear and understandable
- [ ] **Responsibility** - Single responsibility principle followed
- [ ] **Error Handling** - Appropriate error handling implemented
- [ ] **Performance** - Performance implications considered
- [ ] **Security** - Security best practices applied
- [ ] **DRY Principle** - No unnecessary code duplication
- [ ] **Naming** - Consistent naming conventions used
- [ ] **Documentation** - Complex logic well-commented

### 📚 Testing and Documentation
- [ ] **Test Coverage** - New features have corresponding tests
- [ ] **Code Comments** - Complex logic explained
- [ ] **API Documentation** - Changes documented
- [ ] **Usage Examples** - Components have examples
- [ ] **Edge Cases** - Boundary conditions tested
- [ ] **Integration** - Cross-component interactions tested

### 🎨 User Experience
- [ ] UI components are responsive
- [ ] Loading states and error states handled
- [ ] Accessibility considerations (a11y)
- [ ] Dark mode compatibility
- [ ] Proper keyboard navigation
- [ ] Screen reader compatibility

### 🚀 Performance and Optimization
- [ ] **Core Web Vitals** - Meets enterprise standards (LCP≤2.5s, INP≤200ms, CLS≤0.1)
- [ ] **Lighthouse Score** - Performance≥90, Accessibility≥95, Best Practices≥90
- [ ] **Bundle Size** - Initial bundle≤200KB, Total bundle≤1MB
- [ ] **Images optimized** - Using Next.js Image component
- [ ] **Code splitting** - Reasonable implementation
- [ ] **No memory leaks** - Proper cleanup and disposal
- [ ] **Network requests** - Optimized and cached
- [ ] **Lazy loading** - Implemented where appropriate
- [ ] **Web Vitals monitoring** - Real-time performance tracking

### 🔒 Security Assessment
- [ ] **Input Validation** - All user inputs properly validated
- [ ] **XSS Protection** - React's built-in protection utilized
- [ ] **Data Exposure** - No sensitive data in client-side code
- [ ] **Authentication** - Proper auth/authorization implementation
- [ ] **HTTPS Usage** - All external requests use HTTPS
- [ ] **Secrets Management** - No hardcoded secrets or API keys

### 🛡️ Security Integration with Three-Layer Review

#### Layer 1: Automated Security Checks
- [ ] **ESLint Security Rules** - All 12 security-related ESLint rules pass
- [ ] **Custom Security Scan** - `pnpm security:scan` passes (sensitive data detection)
- [ ] **Dependency Audit** - `pnpm security:deps` shows no vulnerabilities (zero vulnerability policy)
- [ ] **Environment Validation** - @t3-oss/env-nextjs validation passes
- [ ] **Type Safety** - No `any` types that could bypass security
- [ ] **Security Thresholds** - Critical:0, High:0, Medium≤5, Low≤10

#### Layer 2: AI Security Review
- [ ] **Code Analysis** - No hardcoded sensitive information detected
- [ ] **Input Handling** - Input validation logic complete and secure
- [ ] **Permission Logic** - Access control implementation correct
- [ ] **Third-party Integration** - External service integrations secure

#### Layer 3: Human Security Confirmation
- [ ] **Security Headers** - Next.js security headers properly configured
- [ ] **User Input Processing** - All user inputs handled securely
- [ ] **Bot Protection** - botid integration working correctly
- [ ] **Data Flow** - Sensitive data flow follows security principles

## Quality Check Commands

### Core Quality Commands
```bash
# Primary commands
pnpm quality:check        # Complete quality validation (required)
pnpm quality:parallel     # Parallel execution (faster)

# Individual validations
pnpm type-check           # TypeScript type validation
pnpm lint:check           # ESLint code standards
pnpm format:check         # Prettier formatting
pnpm test:coverage        # Test coverage (80% minimum)

# Build validation
pnpm build                # Production build verification
```

**Note**: Security and performance commands are documented in `security-performance-standards.md`.

### Development Commands
```bash
# Development
pnpm dev                  # Start development server
pnpm dev --turbopack     # Use Turbopack (faster)

# Building
pnpm build               # Production build
pnpm start               # Start production server

# Testing
pnpm test                # Run tests
pnpm test:ui             # Run tests with UI
pnpm test:run            # Run tests once
```

### Fixing Issues
```bash
# Auto-fix common issues
pnpm lint:fix            # Auto-fix ESLint issues
pnpm format:fix          # Auto-format code

# Combined fix
pnpm format:lint         # Format then lint fix
```

## Troubleshooting Quick Reference

### Common Issues and Solutions

#### Git Hooks Issues
```bash
# Problem: Lefthook hooks not executing
# Solution: Reinstall hooks
pnpm lefthook uninstall
pnpm lefthook install

# Problem: Hooks execution failed
# Solution: Check hooks permissions
chmod +x .git/hooks/*

# Emergency: Skip hooks (use sparingly)
git commit --no-verify -m "emergency fix"
git push --no-verify
```

#### Code Formatting Issues
```bash
# Problem: Prettier and ESLint conflicts
# Solution: Check configuration compatibility
pnpm eslint-config-prettier

# Problem: Import sorting incorrect
# Solution: Run Prettier manually
pnpm prettier --write "src/**/*.{ts,tsx}"

# Problem: Format issues persist after Prettier
# Solution: Run ESLint fix
pnpm lint:fix
```

#### TypeScript Issues
```bash
# Problem: Type checking failed
# Solution: Clear cache and re-check
rm -rf .next
rm -rf node_modules/.cache
pnpm type-check

# Problem: Third-party library types missing
# Solution: Install type definitions
pnpm add -D @types/library-name

# Problem: Path alias resolution failed
# Solution: Check tsconfig.json paths configuration
```

#### Testing Issues
```bash
# Problem: Test coverage insufficient
# Solution: View coverage report
pnpm test:coverage

# Problem: Tests failing in CI
# Solution: Check test environment setup
cat src/test/setup.ts

# Problem: Mocks not working
# Solution: Check mock configuration
vi.mock('@/lib/api', () => ({
  fetchUser: vi.fn(),
}));
```

#### Build and Deployment Issues
```bash
# Problem: Build failed
# Solution: Clean cache and rebuild
rm -rf .next
rm -rf node_modules
pnpm install
pnpm build

# Problem: Environment variables not loaded
# Solution: Check environment configuration
cat .env.local
cat .env.example
```

## Configuration Files Quick Reference

### Core Configuration Files
- `eslint.config.mjs` - ESLint 9.x flat config (164 rules)
- `tsconfig.json` - TypeScript strict mode (15 strict options)
- `prettier.config.js` - Code formatting configuration
- `vitest.config.ts` - Testing framework (80% coverage requirement)
- `tailwind.config.ts` - Tailwind CSS 4.x configuration
- `components.json` - shadcn/ui component configuration
- `lefthook.yml` - Git hooks configuration
- `commitlint.config.js` - Commit message standards

### Important Scripts
- `scripts/automated-checks.js` - Automated quality checking
- `scripts/ai-review.js` - AI code review
- `scripts/human-verification.js` - Human verification process
- `scripts/security-check.js` - Custom security scanning script
- `scripts/dependency-audit.js` - Dependency security audit script
- `scripts/performance-monitor.js` - Performance monitoring script
- `scripts/simple-perf-test.js` - Simple performance test script

### Environment Files
- `.env.local` - Local development environment variables
- `.env.example` - Environment variables template
- `next.config.ts` - Next.js configuration
- `postcss.config.mjs` - PostCSS configuration

## Standards Summary

### Code Quality Standards
- ✅ Zero warning policy: All ESLint, TypeScript checks must pass
- ✅ Test coverage: Requires 80%+ coverage
- ✅ Type safety: Enable TypeScript strict mode
- ✅ Code formatting: Unified Prettier formatting

### Development Process Standards
- ✅ Git Flow workflow: feature/bugfix/hotfix branch strategy
- ✅ Conventional Commits: Standardized commit message format
- ✅ Code review: At least 2 reviewers, approval required for merge
- ✅ Automated checks: Git hooks + CI/CD quality gates

### Technology Stack Standards
- ✅ Next.js 15 + React 19: Modern full-stack framework
- ✅ TypeScript 5: Type-safe development
- ✅ Tailwind CSS 4.x: High-performance styling system
- ✅ shadcn/ui: Enterprise-grade component library
- ✅ Vitest: Modern testing framework

### Performance and Security Standards
- ✅ **Core Web Vitals Enterprise Standards**: LCP ≤ 2.5s, INP ≤ 200ms, CLS ≤ 0.1
- ✅ **Lighthouse Enterprise Scores**: Performance≥90, Accessibility≥95, Best Practices≥90
- ✅ **Three-Layer Security Audit System**: Automated checks + AI review + Human confirmation
- ✅ **Zero Vulnerability Policy**: Critical:0, High:0, Medium≤5, Low≤10
- ✅ **Real-time Performance Monitoring**: Web Vitals + Lighthouse CI + Bundle size monitoring
- ✅ **Code splitting**: Route-level and component-level lazy loading
- ✅ **Image optimization**: Next.js Image component + WebP/AVIF formats

**Note**: Detailed standards are documented in `security-performance-standards.md`.

## Emergency Procedures

### Hotfix Process
1. Create hotfix branch from main
2. Make minimal necessary changes
3. Run quality checks (can skip some in emergency)
4. Get expedited review
5. Deploy immediately
6. Follow up with proper testing

### Quality Check Bypass (Emergency Only)
```bash
# Skip pre-commit hooks
git commit --no-verify -m "hotfix: critical security patch"

# Skip pre-push hooks
git push --no-verify

# Post-emergency cleanup
git checkout -b fix/quality-improvements
# Fix quality issues
pnpm quality:check
git commit -m "fix: resolve quality issues from hotfix"
```

### Rollback Procedures
1. Identify problematic deployment
2. Revert to last known good state
3. Investigate root cause
4. Implement proper fix
5. Re-deploy with full quality checks

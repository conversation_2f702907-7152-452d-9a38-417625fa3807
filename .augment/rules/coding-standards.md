---
type: always
description: "Enterprise-grade coding standards and quality requirements"
---

# Coding Standards

## Zero Warning Policy 🚫

**Enterprise-grade zero tolerance standard**: All code quality checks must pass with no warnings or errors.

```bash
pnpm quality:check  # Must return 0 errors
pnpm lint:check     # Must return 0 warnings
pnpm type-check     # Must return 0 type errors
```

## ESLint Rules (164 Enterprise Rules)

### Core Rule Categories

**TypeScript Strict Rules (33 rules)**
- Prohibit `any` type usage
- Require explicit function return types
- Prevent unsafe type operations
- Enforce strict boolean expressions

**Security Rules (12 rules + Custom Security Audit)**
- Prevent object injection attacks
- Detect unsafe regex patterns
- Prohibit eval usage
- Require literal require statements
- Custom security scanning (sensitive data detection)
- Dependency security audit (zero vulnerability policy)

**Code Complexity Control (6 rules)**
- Cyclomatic complexity: max 15
- Cognitive complexity: max 10
- Function length: max 100 lines
- Parameters: max 6 per function

**Modern JavaScript Rules (18 rules)**
- Prefer Array.find() over filter()[0]
- Use optional chaining (?.)
- Use kebab-case for filenames
- Prefer const over let

**Code Quality Rules (10 rules)**
- Avoid duplicate strings
- Prevent identical functions
- Prohibit console statements
- Use template strings

**Promise and Async Rules (4 rules)**
- Promise chains must return values
- Promises must handle errors
- Prohibit floating promises
- Only await thenable objects

**Import and Module Rules (2 rules)**
- Avoid duplicate imports
- Consistent import organization

## TypeScript Strict Mode (15 Strict Options)

### Essential Strict Settings
```json
{
  "strict": true,
  "noUncheckedIndexedAccess": true,
  "exactOptionalPropertyTypes": true,
  "noImplicitReturns": true,
  "noFallthroughCasesInSwitch": true,
  "noImplicitOverride": true,
  "noPropertyAccessFromIndexSignature": true,
  "noUncheckedSideEffectImports": true,
  "allowUnusedLabels": false,
  "allowUnreachableCode": false
}
```

### Type Safety Principles
- **No `any` type** - Use specific types or `unknown`
- **Explicit returns** - All functions must declare return types
- **Type guards** - Implement runtime type checking
- **External data** - Use `unknown` with validation
- **Object shapes** - Prefer interfaces over types
- **Complex types** - Use type aliases for unions/intersections

## Next.js 15 + React 19 Development Standards

### Component Architecture Principles
- **Server Components first** - Default choice for all components
- **Client Components** - Only for interactivity (`"use client"`)
- **Error boundaries** - Wrap async components
- **Suspense** - Handle loading states gracefully

### Naming Conventions
- **Files**: kebab-case (`user-profile.tsx`)
- **Components**: PascalCase (`UserProfile`)
- **Functions**: camelCase (`getUserData`)
- **Constants**: SCREAMING_SNAKE_CASE (`API_ENDPOINTS`)

### Component Structure Template
```typescript
interface ComponentProps {
  readonly prop: string;
}

export function Component({ prop }: ComponentProps): ReactElement {
  return <div>{prop}</div>;
}
```

### Data Fetching Strategy
- **Server Components** - Primary data fetching method
- **Caching** - Use `next/cache` for optimization
- **Revalidation** - Use `revalidateTag` for cache invalidation
- **States** - Handle loading and error scenarios

## Tailwind CSS 4.x + shadcn/ui Standards

### CSS-first Mode Usage
- Use semantic color variables
- Implement responsive design mobile-first
- Use design tokens from tailwind.config.ts
- Leverage shadcn/ui components

### Component Styling
- Use `cn()` utility for conditional classes
- Implement consistent spacing system
- Use Tailwind's responsive variants
- Avoid custom CSS unless absolutely necessary

### Design System
```typescript
// ✅ Correct styling approach
<div className={cn(
  "base-styles",
  condition && "conditional-styles",
  className
)}>
```

## Testing Requirements

### Coverage Standards
- **Minimum 80% test coverage** required
- Unit tests for all utility functions
- Integration tests for components
- E2E tests for critical user flows

### Vitest Testing Standards

#### Test Organization
- **File Naming**: `*.test.ts` or `*.spec.ts`
- **Test Structure**: Arrange-Act-Assert pattern
- **Mock Strategy**: Use `vi.mock()` for external dependencies
- **Test Location**: Co-locate tests with source files or in `__tests__` directory

#### Testing Framework Configuration
- Use Vitest for unit and integration tests
- Use Testing Library for component tests
- Use jsdom environment for DOM testing
- Mock external dependencies properly

#### Test Structure Template
```typescript
// ✅ Correct test structure
describe('Component', () => {
  it('should render correctly', () => {
    // Arrange
    const props = { title: 'Test' };

    // Act
    render(<Component {...props} />);

    // Assert
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

#### Mock Best Practices
```typescript
// ✅ External dependency mocking
vi.mock('@/lib/api', () => ({
  fetchUser: vi.fn(),
  updateUser: vi.fn(),
}));

// ✅ Module mocking
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}));
```

## Animation System Standards

### Hybrid Animation Strategy
- **Simple animations**: Use Tailwind CSS classes (fade-in, slide-in, scale)
- **Scroll-driven animations**: Start with Tailwind CSS + Intersection Observer, consider framer-motion if effects are insufficient
- **Complex interactions**: Use framer-motion for advanced features when necessary
- **Performance priority**: Default to Tailwind CSS, enhance with Motion only when proven necessary

### Tailwind CSS Animation Usage
```typescript
// ✅ Basic animations - Use Tailwind CSS
<div className="animate-fade-in hover:scale-105 transition-transform duration-300">
  Basic interactive element
</div>

// ✅ Combined animations - CSS with timing control
<div className="animate-slide-in-from-bottom delay-100 duration-500">
  Staggered animation element
</div>

// ✅ Responsive animations
<div className="animate-slide-in-from-top md:animate-fade-in">
  Different animations per breakpoint
</div>
```

### Scroll-Driven Animation Implementation

#### Option 1: Tailwind CSS + Intersection Observer (Recommended First)
```typescript
// ✅ Lightweight scroll-driven animations
import { useEffect, useState, useRef } from 'react'

const useInView = (threshold = 0.1) => {
  const [inView, setInView] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold }
    )
    if (ref.current) observer.observe(ref.current)
    return () => observer.disconnect()
  }, [threshold])

  return { ref, inView }
}

// Usage with Tailwind CSS
const { ref, inView } = useInView()
<div
  ref={ref}
  className={`transition-all duration-600 ${
    inView ? 'animate-fade-in' : 'opacity-0 translate-y-12'
  }`}
>
  Scroll-triggered content
</div>
```

#### Option 2: Motion (framer-motion) - If Option 1 is Insufficient
```typescript
// ✅ Advanced scroll-driven animations - Use when Tailwind CSS approach is limited
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'

const { ref, inView } = useInView({
  threshold: 0.1,
  triggerOnce: true
})

<motion.div
  ref={ref}
  initial={{ opacity: 0, y: 50 }}
  animate={inView ? { opacity: 1, y: 0 } : {}}
  transition={{ duration: 0.6, ease: "easeOut" }}
>
  Complex scroll-triggered content
</motion.div>
```

### Accessibility Requirements
```typescript
// ✅ Reduced motion support
const useReducedMotion = () => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Apply in Motion components
<motion.div
  animate={useReducedMotion() ? {} : { x: 100 }}
  transition={useReducedMotion() ? { duration: 0 } : { duration: 0.5 }}
>

// Apply in Tailwind CSS
<div className="motion-safe:animate-fade-in motion-reduce:animate-none">
  Accessible animation
</div>
```

### Animation Performance Guidelines
- **GPU Acceleration**: Prefer `transform` and `opacity` properties
- **Avoid Layout Thrashing**: Don't animate `width`, `height`, `top`, `left`
- **Use `will-change`**: Only when necessary and remove after animation
- **Batch Animations**: Group multiple animations to reduce reflows
- **Duration Guidelines**: 200-300ms for micro-interactions, 400-600ms for content transitions

### Installation and Setup
```bash
# Step 1: Use existing Tailwind CSS animations (no installation needed)
# Current animations: accordion-down, accordion-up, fade-in, slide-in-from-top, slide-in-from-bottom

# Step 2: Try Tailwind CSS + Intersection Observer for scroll effects
# No additional packages needed - use native browser API

# Step 3: Install framer-motion only if Tailwind CSS approach is insufficient
# pnpm add framer-motion@latest
```

### Decision Framework
```typescript
// ✅ Start with this approach
const scrollAnimation = useTailwindScrollAnimation()

// ✅ Upgrade to this only if needed
// const scrollAnimation = useFramerMotionScrollAnimation()
```

## Security Standards

**Note**: Detailed security standards are maintained in `security-performance-standards.md`. This section covers coding-level security requirements.

### Input Validation
- Validate all user inputs using type-safe APIs
- Sanitize data before processing
- Implement proper error handling
- Use schema validation for external data

### XSS Prevention
- Use React's built-in XSS protection
- Avoid `dangerouslySetInnerHTML` unless absolutely necessary
- Validate and sanitize external content
- Implement Content Security Policy headers

### Type Safety for Security
- Prohibit `any` type usage that bypasses security
- Use `unknown` type for external data with validation
- Implement runtime type guards for user inputs
- Validate environment variables with @t3-oss/env-nextjs

### Input Validation
- Validate all user inputs
- Sanitize data before processing
- Use type-safe APIs
- Implement proper error handling

### XSS Prevention
- Use React's built-in XSS protection
- Avoid `dangerouslySetInnerHTML`
- Validate and sanitize external content
- Implement Content Security Policy

### Authentication & Authorization
- Use secure session management
- Implement proper RBAC
- Validate permissions on server-side
- Use HTTPS for all communications

## Performance Standards

**Note**: Comprehensive performance monitoring standards are detailed in `security-performance-standards.md`. This section covers coding-level performance requirements.

### Code-Level Performance Requirements
- Use Next.js Image component for all images
- Implement proper code splitting and lazy loading
- Optimize bundle size with tree shaking
- Use appropriate caching strategies
- Avoid blocking the main thread
- Implement proper error boundaries

### Bundle Size Guidelines
- Initial bundle: ≤ 200KB
- Total bundle: ≤ 1MB
- Monitor bundle size changes in CI/CD
- Use dynamic imports for large dependencies

### Optimization Techniques
- Use Next.js Image component for images
- Implement code splitting and lazy loading
- Optimize bundle size with tree shaking
- Use proper caching strategies
- Implement Web Vitals real-time monitoring
- Use performance budgets and alerts

## Quality Check Commands

```bash
# Core quality checks
pnpm quality:check          # Complete code quality validation
pnpm quality:parallel       # Parallel execution (faster)

# Individual checks
pnpm type-check             # TypeScript type validation
pnpm lint:check             # ESLint code standards
pnpm format:check           # Prettier formatting
pnpm test:coverage          # Test coverage (80% minimum)

# Build validation
pnpm build                  # Production build verification
```

**Note**: Security and performance check commands are documented in `security-performance-standards.md` and `project-guidelines.md`.

## Configuration References

- `eslint.config.mjs` - ESLint 9.x flat config (164 rules)
- `tsconfig.json` - TypeScript strict mode (15 options)
- `prettier.config.js` - Code formatting
- `vitest.config.ts` - Testing framework (80% coverage)
- `lefthook.yml` - Git hooks automation

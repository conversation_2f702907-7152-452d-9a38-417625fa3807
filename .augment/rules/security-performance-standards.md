---
type: always
description: "Enterprise-grade security audit and performance monitoring standards"
---

# Security & Performance Standards

## 🔒 Three-Layer Security Audit System

### Overview
Enterprise-grade security assurance through a three-layer defense system ensuring code and dependency security:
1. **Automated Foundation Checks** - ESLint security rules, dependency vulnerability scanning
2. **AI Technical Review** - Code security analysis, best practice assessment
3. **Human Simplified Confirmation** - Final security acceptance

### Layer 1: Automated Foundation Checks

#### ESLint Security Rules (12 rules)
```bash
# Required security rules that must pass
security/detect-object-injection      # Object injection attack detection
security/detect-non-literal-fs-filename # File system operation risks
security/detect-unsafe-regex         # Unsafe regex patterns
security/detect-buffer-noassert      # Buffer operation risks
security/detect-child-process        # Child process execution risks
security/detect-eval-with-expression # eval usage detection
security/detect-non-literal-require  # Dynamic require detection
security/detect-possible-timing-attacks # Timing attack risks
```

#### Custom Security Scanning
```bash
pnpm security:scan  # Run custom security scanning
```

**Scan Coverage:**
- Sensitive information leakage (passwords, keys, tokens, etc.)
- Unsafe function calls (eval, innerHTML, etc.)
- Dynamic import security risks
- File permission checks

#### Dependency Security Audit
```bash
pnpm security:deps  # Run dependency security audit
```

**Severity Thresholds (Zero Tolerance Policy):**
- **Critical Vulnerabilities**: 0 (not allowed)
- **High Vulnerabilities**: 0 (not allowed)
- **Medium Vulnerabilities**: Maximum 5
- **Low Vulnerabilities**: Maximum 10

### Layer 2: AI Technical Review

#### Code Security Analysis
- Security configuration rationality assessment
- Best practice evaluation
- Vulnerability pattern identification
- Potential security risk detection

#### Implementation Method
```bash
pnpm ai:security  # AI security review
```

### Layer 3: Human Simplified Confirmation

#### Security Acceptance Checklist
- [ ] Security check scripts validation passed
- [ ] Dependency audit report shows no high-risk vulnerabilities
- [ ] Security configuration check correct
- [ ] Sensitive information handling compliant

## 🚀 Enterprise Performance Monitoring System

### Overview
Enterprise performance assurance through a three-layer monitoring system ensuring websites meet high-performance standards:
1. **Automated Foundation Checks** - Lighthouse CI, Web Vitals collection, bundle size analysis
2. **AI Technical Review** - Performance configuration rationality assessment, optimization suggestion analysis
3. **Human Simplified Confirmation** - Performance report viewing, metrics verification

### Core Web Vitals Enterprise Standards (2024 Latest)

#### Key Metrics Requirements
- **LCP (Largest Contentful Paint)**: ≤ 2.5 seconds
- **INP (Interaction to Next Paint)**: ≤ 200 milliseconds (replaces FID)
- **CLS (Cumulative Layout Shift)**: ≤ 0.1
- **FCP (First Contentful Paint)**: ≤ 1.8 seconds
- **TTFB (Time to First Byte)**: ≤ 800 milliseconds

#### Lighthouse Enterprise Score Requirements
- **Performance**: ≥ 90/100
- **Accessibility**: ≥ 95/100
- **Best Practices**: ≥ 90/100
- **SEO**: ≥ 90/100
- **PWA**: ≥ 80/100

#### Bundle Size Limits
- **Initial Bundle Size**: ≤ 200KB
- **Total Bundle Size**: ≤ 1MB

### Performance Monitoring Tools

#### Lighthouse CI
```bash
pnpm lighthouse:ci  # Run Lighthouse CI testing
```

**Configuration File**: `lighthouserc.cjs`
- Enterprise performance standards configuration
- Mobile device simulation testing
- Automated server startup
- Detailed performance assertion rules

#### Web Vitals Real-time Monitoring
```bash
# Integration into React application
import { WebVitalsProvider } from '@/components/performance/web-vitals-provider';
```

**Feature Characteristics:**
- Real-time performance data collection
- Automated analysis service integration
- Development environment debug panel
- Performance metrics hook support

#### Performance Testing Scripts
```bash
pnpm perf:test     # Simple performance testing
pnpm perf:monitor  # Complete performance monitoring
pnpm perf:check    # Basic performance checking
```

## 🛠️ Integrated Development Workflow

### Pre-Development Checks
```bash
pnpm quality:check && pnpm security:check && pnpm perf:test
```

### Pre-Commit Checks (Git Hooks)
```yaml
# lefthook.yml configuration
pre-commit:
  commands:
    security-scan:
      run: pnpm security:scan
    dependency-audit:
      run: pnpm security:deps
```

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Security & Performance Check
  run: |
    pnpm security:full
    pnpm perf:check
```

## 📊 Quality Gate Standards

### Required Checks That Must Pass
- [ ] **Code Quality**: `pnpm quality:check` passes
- [ ] **Security Check**: `pnpm security:check` passes (zero vulnerabilities)
- [ ] **Performance Test**: `pnpm perf:test` passes (enterprise standards)
- [ ] **Build Verification**: `pnpm build` succeeds
- [ ] **Type Check**: `pnpm type-check` no errors

### Performance Gates
- Lighthouse performance score ≥ 90
- All Core Web Vitals meet standards
- Bundle size within limits
- Build time < 60 seconds

### Security Gates
- All ESLint security rules pass
- Custom security scan shows no issues
- Dependency audit shows no high-risk vulnerabilities
- Sensitive information check passes

## 🔧 Troubleshooting

### Security Check Failures
```bash
# View detailed security report
pnpm security:scan

# Fix dependency vulnerabilities
pnpm audit --fix

# Update outdated dependencies
pnpm update
```

### Performance Test Failures
```bash
# View performance report
pnpm perf:monitor

# Analyze bundle size
pnpm build && npx @next/bundle-analyzer

# Check Core Web Vitals
# View Performance panel in browser developer tools
```

## 📚 Related Documentation

### Configuration Files
- `lighthouserc.cjs` - Lighthouse CI configuration
- `scripts/security-check.js` - Custom security scanning
- `scripts/dependency-audit.js` - Dependency security audit
- `scripts/performance-monitor.js` - Performance monitoring
- `src/lib/web-vitals.ts` - Web Vitals library

### Usage Guides
- `docs/security-audit-guide.md` - Security audit guide
- `docs/performance-monitoring-guide.md` - Performance monitoring guide

### Cross-References
- See `coding-standards.md` for code-level security and performance requirements
- See `project-guidelines.md` for development workflow integration
- See `review-checklist.md` for practical checklists and troubleshooting

## 🎯 Best Practices

### Security Best Practices
1. **Regular Security Checks** - Run security scans daily
2. **Dependency Management** - Regularly update and audit dependencies
3. **Sensitive Information Management** - Use environment variables, never commit sensitive information
4. **Code Review** - Pay attention to security-related ESLint warnings

### Performance Best Practices
1. **Continuous Monitoring** - Integrate Web Vitals real-time monitoring
2. **Performance Budgets** - Strictly control bundle size and performance metrics
3. **Optimization Strategies** - Image optimization, code splitting, caching strategies
4. **Regular Testing** - Run performance tests after each build

## 🚨 Emergency Procedures

### Security Vulnerability Discovery
1. Immediately stop related feature deployment
2. Assess vulnerability impact scope
3. Implement temporary mitigation measures
4. Develop permanent fix solution
5. Update security check rules

### Severe Performance Degradation
1. Rollback to previous stable version
2. Analyze performance degradation causes
3. Implement performance optimization measures
4. Verify fix effectiveness
5. Redeploy and monitor

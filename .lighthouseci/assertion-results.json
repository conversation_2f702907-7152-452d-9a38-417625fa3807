[{"name": "minScore", "expected": 0.9, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "auditId": "bf-cache", "level": "error", "url": "http://localhost:3000/en", "auditTitle": "Page prevented back/forward cache restoration", "auditDocumentationLink": "https://developer.chrome.com/docs/lighthouse/performance/bf-cache/"}, {"name": "minScore", "expected": 0.9, "actual": 0.5, "values": [0.5, 0.5, 0.5], "operator": ">=", "passed": false, "auditId": "document-latency-insight", "level": "error", "url": "http://localhost:3000/en", "auditTitle": "Document request latency"}, {"name": "minScore", "expected": 0.9, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "auditId": "heading-order", "level": "error", "url": "http://localhost:3000/en", "auditTitle": "Heading elements are not in a sequentially-descending order", "auditDocumentationLink": "https://dequeuniversity.com/rules/axe/4.10/heading-order"}, {"name": "minScore", "expected": 0.9, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "auditId": "legacy-javascript-insight", "level": "error", "url": "http://localhost:3000/en", "auditTitle": "Legacy JavaScript", "auditDocumentationLink": "https://web.dev/articles/baseline-and-polyfills"}, {"name": "max<PERSON><PERSON><PERSON>", "expected": 0, "actual": 1, "values": [1, 1, 1], "operator": "<=", "passed": false, "auditId": "legacy-javascript", "level": "warn", "url": "http://localhost:3000/en", "auditTitle": "Avoid serving legacy JavaScript to modern browsers", "auditDocumentationLink": "https://web.dev/baseline"}, {"name": "max<PERSON><PERSON><PERSON>", "expected": 0, "actual": 2, "values": [2, 2, 2], "operator": "<=", "passed": false, "auditId": "render-blocking-insight", "level": "warn", "url": "http://localhost:3000/en", "auditTitle": "Render blocking requests", "auditDocumentationLink": "https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources"}, {"name": "max<PERSON><PERSON><PERSON>", "expected": 0, "actual": 1, "values": [1, 1, 1], "operator": "<=", "passed": false, "auditId": "render-blocking-resources", "level": "warn", "url": "http://localhost:3000/en", "auditTitle": "Eliminate render-blocking resources", "auditDocumentationLink": "https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/"}, {"name": "max<PERSON><PERSON><PERSON>", "expected": 0, "actual": 2, "values": [2, 2, 2], "operator": "<=", "passed": false, "auditId": "unused-javascript", "level": "warn", "url": "http://localhost:3000/en", "auditTitle": "Reduce unused JavaScript", "auditDocumentationLink": "https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/"}, {"name": "minScore", "expected": 0.9, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "auditId": "redirects", "level": "error", "url": "http://localhost:3000/en", "auditTitle": "Avoid multiple page redirects", "auditDocumentationLink": "https://developer.chrome.com/docs/lighthouse/performance/redirects/"}, {"name": "auditRan", "expected": 1, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "auditProperty": "pwa", "auditId": "categories", "level": "warn", "url": "http://localhost:3000/en"}, {"name": "auditRan", "expected": 1, "actual": 0, "values": [0, 0, 0], "operator": ">=", "passed": false, "message": "\"uses-webp-images\" is not a known audit.", "auditId": "uses-webp-images", "level": "warn", "url": "http://localhost:3000/en"}]
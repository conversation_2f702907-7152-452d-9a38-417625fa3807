import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

import { ContactForm } from '@/components/content/contact-form';





/* * 联系页面 * 展示联系表单的页面，提供完整的联系功能。 * 这是一个隐藏页面，不在导航菜单中显示，但可以通过直接 URL 访问。 * 支持多语言显示，配置了完整的SEO metadata。 * 集成了低摩擦度的联系表单组件。
 */

interface ContactPageProps {
  params: Promise<{ locale: string }>;
}
/* * 生成页面metadata
 */

export async function generateMetadata({
  params,
}: ContactPageProps): Promise<Metadata> {
  const { locale } = await params;

  const t = await getTranslations({ locale, namespace: 'pages.contact' });
  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    robots: { index: true, follow: true },
  };
}
/* * 联系页面组件 * 特性： * - 隐藏页面：不在导航菜单中显示 * - 直接访问：可通过 /contact URL 访问 * - 多语言支持：支持中英文切换 * - SEO 优化：完整的 metadata 配置 * - 联系表单：低摩擦度的用户体验设计 * - 响应式设计：适配所有设备
 */

export default function ContactPage({
  params: _params,
}: ContactPageProps): React.JSX.Element {
  return (
    <div className='container mx-auto px-4 py-8'>
      {' '}
      <ContactForm />{' '}
    </div>
  );
}

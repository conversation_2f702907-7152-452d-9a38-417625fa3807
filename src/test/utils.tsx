import { RenderOptions, render } from '@testing-library/react';
import React, { ReactElement } from 'react';
import { vi } from 'vitest';

// 自定义渲染函数的选项类型
type CustomRenderOptions = Omit<RenderOptions, 'wrapper'>;

// 创建一个包装器组件，用于提供测试所需的上下文
const AllTheProviders = ({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement => {
  return <div data-testid='test-wrapper'>{children}</div>;
};

// 自定义渲染函数
const customRender = (
  ui: ReactElement,
  options?: CustomRenderOptions
): ReturnType<typeof render> =>
  render(ui, { wrapper: AllTheProviders, ...options });

// 重新导出所有 testing-library 的工具
export * from '@testing-library/react';

// 覆盖默认的 render 函数
export { customRender as render };

// 常用的测试工具函数
export const createMockComponent = (
  name: string
): React.ComponentType<React.PropsWithChildren<Record<string, unknown>>> => {
  const MockComponent = ({
    children,
    ...props
  }: React.PropsWithChildren<Record<string, unknown>>): React.ReactElement => (
    <div data-testid={`mock-${name.toLowerCase()}`} {...props}>
      {children}
    </div>
  );
  MockComponent.displayName = `Mock${name}`;
  return MockComponent;
};

// 等待异步操作完成的工具函数
// @deprecated 使用 @testing-library/react 的 waitFor 替代，避免测试不稳定
// 仅在确定只需要等待单个事件循环tick时使用
export const waitForAsync = (): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, 0));

// Next.js 路由器类型定义
interface MockRouter {
  push: ReturnType<typeof vi.fn>;
  replace: ReturnType<typeof vi.fn>;
  prefetch: ReturnType<typeof vi.fn>;
  back: ReturnType<typeof vi.fn>;
  forward: ReturnType<typeof vi.fn>;
  refresh: ReturnType<typeof vi.fn>;
  pathname: string;
  route: string;
  query: Record<string, unknown>;
  asPath: string;
}

// 模拟 Next.js 路由的工具函数
export const mockNextRouter = (
  router: Partial<MockRouter> = {}
): MockRouter => {
  return {
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    pathname: '/',
    route: '/',
    query: {},
    asPath: '/',
    ...router,
  };
};

// 创建测试用的事件对象
export const createMockEvent = (
  overrides: Partial<Event> = {}
): Partial<Event> => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: null,
  currentTarget: null,
  ...overrides,
});

// 测试用的延迟函数
export const delay = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';

/**
 * next-intl 4.3.4 路由配置
 * 支持英语（en）和中文（zh-CN）双语
 */
export const routing = defineRouting({
  // 支持的语言列表
  locales: ['en', 'zh'],

  // 默认语言
  defaultLocale: 'en',

  // 路径名配置（可选）
  pathnames: {
    '/': '/',
    '/about': {
      en: '/about',
      zh: '/about',
    },
    '/contact': {
      en: '/contact',
      zh: '/contact',
    },
  },
});

// 类型安全的导航函数
export const { Link, redirect, usePathname, useRouter } =
  createNavigation(routing);

/**
 * 无障碍主题样式
 * 
 * 功能特性：
 * - 高对比度主题
 * - 大字体支持
 * - 减少动画模式
 * - 色盲友好颜色
 * - 增强焦点指示器
 * - 屏幕阅读器优化
 */

/* CSS 自定义属性 */
:root {
  /* 无障碍字体缩放 */
  --accessibility-font-scale: 1;
  
  /* 动画持续时间倍数 */
  --animation-duration-multiplier: 1;
  
  /* 高对比度颜色 */
  --accessibility-high-contrast-bg: #000000;
  --accessibility-high-contrast-fg: #ffffff;
  --accessibility-high-contrast-border: #ffffff;
  --accessibility-high-contrast-focus: #ffff00;
  
  /* 色盲友好颜色 */
  --accessibility-colorblind-primary: #0066cc;
  --accessibility-colorblind-secondary: #ff6600;
  --accessibility-colorblind-success: #228b22;
  --accessibility-colorblind-warning: #ffa500;
  --accessibility-colorblind-error: #dc143c;
  
  /* 焦点指示器 */
  --accessibility-focus-width: 2px;
  --accessibility-focus-offset: 2px;
  --accessibility-focus-color: #005fcc;
}

/* 字体缩放 */
.accessibility-large-text,
:root[data-accessibility-large-text="true"] {
  font-size: calc(1rem * var(--accessibility-font-scale));
}

.accessibility-large-text * {
  font-size: inherit;
}

/* 高对比度模式 */
.accessibility-high-contrast {
  /* 基础颜色重置 */
  background-color: var(--accessibility-high-contrast-bg) !important;
  color: var(--accessibility-high-contrast-fg) !important;
}

.accessibility-high-contrast *:not(svg):not(path) {
  background-color: transparent !important;
  color: var(--accessibility-high-contrast-fg) !important;
  border-color: var(--accessibility-high-contrast-border) !important;
}

/* 高对比度级别 */
.accessibility-high-contrast[data-contrast-level="high"] {
  --accessibility-high-contrast-bg: #000000;
  --accessibility-high-contrast-fg: #ffffff;
}

.accessibility-high-contrast[data-contrast-level="higher"] {
  --accessibility-high-contrast-bg: #000000;
  --accessibility-high-contrast-fg: #ffffff;
  filter: contrast(1.5);
}

.accessibility-high-contrast[data-contrast-level="maximum"] {
  --accessibility-high-contrast-bg: #000000;
  --accessibility-high-contrast-fg: #ffffff;
  filter: contrast(2.0);
}

/* 高对比度按钮样式 */
.accessibility-high-contrast button,
.accessibility-high-contrast .btn {
  background-color: var(--accessibility-high-contrast-fg) !important;
  color: var(--accessibility-high-contrast-bg) !important;
  border: 2px solid var(--accessibility-high-contrast-fg) !important;
}

.accessibility-high-contrast button:hover,
.accessibility-high-contrast .btn:hover {
  background-color: var(--accessibility-high-contrast-bg) !important;
  color: var(--accessibility-high-contrast-fg) !important;
}

/* 高对比度链接样式 */
.accessibility-high-contrast a {
  color: var(--accessibility-high-contrast-focus) !important;
  text-decoration: underline !important;
}

.accessibility-high-contrast a:hover {
  background-color: var(--accessibility-high-contrast-focus) !important;
  color: var(--accessibility-high-contrast-bg) !important;
}

/* 减少动画模式 */
.accessibility-reduced-motion,
.accessibility-reduced-motion *,
.accessibility-reduced-motion *::before,
.accessibility-reduced-motion *::after {
  animation-duration: calc(0.01ms * var(--animation-duration-multiplier)) !important;
  animation-iteration-count: 1 !important;
  transition-duration: calc(0.01ms * var(--animation-duration-multiplier)) !important;
  scroll-behavior: auto !important;
}

/* 当动画持续时间倍数为 0 时完全禁用动画 */
:root[style*="--animation-duration-multiplier: 0"] .accessibility-reduced-motion,
:root[style*="--animation-duration-multiplier: 0"] .accessibility-reduced-motion *,
:root[style*="--animation-duration-multiplier: 0"] .accessibility-reduced-motion *::before,
:root[style*="--animation-duration-multiplier: 0"] .accessibility-reduced-motion *::after {
  animation: none !important;
  transition: none !important;
}

/* 色盲友好模式 */
.accessibility-color-blind-friendly {
  /* 使用色盲友好的颜色调色板 */
}

.accessibility-color-blind-friendly[data-color-blind-type="protanopia"] {
  /* 红色盲优化 */
  filter: url('#protanopia-filter');
}

.accessibility-color-blind-friendly[data-color-blind-type="deuteranopia"] {
  /* 绿色盲优化 */
  filter: url('#deuteranopia-filter');
}

.accessibility-color-blind-friendly[data-color-blind-type="tritanopia"] {
  /* 蓝色盲优化 */
  filter: url('#tritanopia-filter');
}

.accessibility-color-blind-friendly[data-color-blind-type="achromatopsia"] {
  /* 全色盲优化 */
  filter: grayscale(1);
}

/* 色盲友好颜色替换 */
.accessibility-color-blind-friendly .text-primary,
.accessibility-color-blind-friendly .bg-primary {
  color: var(--accessibility-colorblind-primary) !important;
  background-color: var(--accessibility-colorblind-primary) !important;
}

.accessibility-color-blind-friendly .text-secondary,
.accessibility-color-blind-friendly .bg-secondary {
  color: var(--accessibility-colorblind-secondary) !important;
  background-color: var(--accessibility-colorblind-secondary) !important;
}

.accessibility-color-blind-friendly .text-success,
.accessibility-color-blind-friendly .bg-success {
  color: var(--accessibility-colorblind-success) !important;
  background-color: var(--accessibility-colorblind-success) !important;
}

.accessibility-color-blind-friendly .text-warning,
.accessibility-color-blind-friendly .bg-warning {
  color: var(--accessibility-colorblind-warning) !important;
  background-color: var(--accessibility-colorblind-warning) !important;
}

.accessibility-color-blind-friendly .text-error,
.accessibility-color-blind-friendly .text-destructive,
.accessibility-color-blind-friendly .bg-error,
.accessibility-color-blind-friendly .bg-destructive {
  color: var(--accessibility-colorblind-error) !important;
  background-color: var(--accessibility-colorblind-error) !important;
}

/* 增强焦点指示器 */
.accessibility-enhanced-focus *:focus,
.accessibility-enhanced-focus *:focus-visible {
  outline: var(--accessibility-focus-width) solid var(--accessibility-focus-color) !important;
  outline-offset: var(--accessibility-focus-offset) !important;
  box-shadow: 0 0 0 calc(var(--accessibility-focus-width) + var(--accessibility-focus-offset)) rgba(0, 95, 204, 0.3) !important;
}

.accessibility-enhanced-focus button:focus,
.accessibility-enhanced-focus a:focus,
.accessibility-enhanced-focus input:focus,
.accessibility-enhanced-focus textarea:focus,
.accessibility-enhanced-focus select:focus {
  outline: 3px solid var(--accessibility-focus-color) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 5px rgba(0, 95, 204, 0.3) !important;
}

/* 屏幕阅读器优化 */
.accessibility-screen-reader-optimized {
  /* 为屏幕阅读器优化的样式 */
}

.accessibility-screen-reader-optimized .sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.accessibility-screen-reader-optimized .sr-only:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0.5rem !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
  background-color: var(--accessibility-high-contrast-bg) !important;
  color: var(--accessibility-high-contrast-fg) !important;
  border: 2px solid var(--accessibility-focus-color) !important;
  z-index: 9999 !important;
}

/* 跳转链接 */
.accessibility-screen-reader-optimized .skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--accessibility-high-contrast-bg);
  color: var(--accessibility-high-contrast-fg);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
  transition: top 0.3s;
}

.accessibility-screen-reader-optimized .skip-link:focus {
  top: 6px;
}

/* 响应式字体缩放 */
@media (max-width: 768px) {
  .accessibility-large-text {
    font-size: calc(0.9rem * var(--accessibility-font-scale));
  }
}

@media (max-width: 480px) {
  .accessibility-large-text {
    font-size: calc(0.8rem * var(--accessibility-font-scale));
  }
}

/* 打印样式优化 */
@media print {
  .accessibility-high-contrast {
    background-color: white !important;
    color: black !important;
    filter: none !important;
  }
  
  .accessibility-high-contrast * {
    background-color: transparent !important;
    color: black !important;
    border-color: black !important;
  }
}

/* 强制颜色模式支持 */
@media (forced-colors: active) {
  .accessibility-high-contrast {
    forced-color-adjust: none;
  }
  
  .accessibility-high-contrast *:focus {
    outline: 2px solid Highlight !important;
    outline-offset: 2px !important;
  }
}

/* 色盲滤镜 SVG 定义 */
.accessibility-colorblind-filters {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}

/**
 * 主题过渡动画样式
 * 
 * 功能特性：
 * - CSS 变量驱动的过渡效果
 * - 硬件加速优化
 * - 响应式动画适配
 * - 无障碍支持
 * - 性能优化
 */

/* CSS 变量定义 */
:root {
  /* 过渡动画变量 */
  --theme-transition-opacity: 1;
  --theme-transition-translate-x: 0%;
  --theme-transition-translate-y: 0%;
  --theme-transition-scale: 1;
  --theme-transition-rotate-x: 0deg;
  --theme-transition-rotate-y: 0deg;
  --theme-transition-rotate-z: 0deg;
  --theme-transition-skew-x: 0deg;
  --theme-transition-skew-y: 0deg;
  --theme-transition-blur: 0px;
  --theme-transition-brightness: 1;
  --theme-transition-contrast: 1;
  --theme-transition-saturate: 1;
  --theme-transition-hue-rotate: 0deg;
  
  /* 动画时序变量 */
  --theme-transition-duration: 300ms;
  --theme-transition-delay: 0ms;
  --theme-transition-timing: ease-out;
  
  /* 动画状态变量 */
  --theme-transition-active: 0;
  --theme-transition-progress: 0;
}

/* 基础过渡样式 */
.theme-transition-container {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  
  /* 基础过渡属性 */
  transition: 
    opacity var(--theme-transition-duration) var(--theme-transition-timing) var(--theme-transition-delay),
    transform var(--theme-transition-duration) var(--theme-transition-timing) var(--theme-transition-delay),
    filter var(--theme-transition-duration) var(--theme-transition-timing) var(--theme-transition-delay);
  
  /* 应用过渡变换 */
  opacity: var(--theme-transition-opacity);
  transform: 
    translateX(var(--theme-transition-translate-x))
    translateY(var(--theme-transition-translate-y))
    scale(var(--theme-transition-scale))
    rotateX(var(--theme-transition-rotate-x))
    rotateY(var(--theme-transition-rotate-y))
    rotateZ(var(--theme-transition-rotate-z))
    skewX(var(--theme-transition-skew-x))
    skewY(var(--theme-transition-skew-y));
  
  filter: 
    blur(var(--theme-transition-blur))
    brightness(var(--theme-transition-brightness))
    contrast(var(--theme-transition-contrast))
    saturate(var(--theme-transition-saturate))
    hue-rotate(var(--theme-transition-hue-rotate));
}

/* 淡入淡出过渡 */
.theme-transition-fade {
  transition: opacity var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-fade.transitioning {
  opacity: var(--theme-transition-opacity);
}

/* 滑动过渡 */
.theme-transition-slide {
  transition: transform var(--theme-transition-duration) var(--theme-transition-timing);
  overflow: hidden;
}

.theme-transition-slide.transitioning {
  transform: translateX(var(--theme-transition-translate-x));
}

/* 缩放过渡 */
.theme-transition-scale {
  transition: transform var(--theme-transition-duration) var(--theme-transition-timing);
  transform-origin: center;
}

.theme-transition-scale.transitioning {
  transform: scale(var(--theme-transition-scale));
}

/* 翻转过渡 */
.theme-transition-flip {
  transition: transform var(--theme-transition-duration) var(--theme-transition-timing);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.theme-transition-flip.transitioning {
  transform: rotateY(var(--theme-transition-rotate-y));
}

/* 溶解过渡 */
.theme-transition-dissolve {
  transition: filter var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-dissolve.transitioning {
  filter: blur(var(--theme-transition-blur));
}

/* 变形过渡 */
.theme-transition-morph {
  transition: transform var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-morph.transitioning {
  transform: skewX(var(--theme-transition-skew-x));
}

/* 组合过渡效果 */
.theme-transition-complex {
  transition: 
    opacity var(--theme-transition-duration) var(--theme-transition-timing),
    transform var(--theme-transition-duration) var(--theme-transition-timing),
    filter var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-complex.transitioning {
  opacity: var(--theme-transition-opacity);
  transform: 
    translateX(var(--theme-transition-translate-x))
    translateY(var(--theme-transition-translate-y))
    scale(var(--theme-transition-scale))
    rotateY(var(--theme-transition-rotate-y))
    skewX(var(--theme-transition-skew-x));
  filter: 
    blur(var(--theme-transition-blur))
    brightness(var(--theme-transition-brightness));
}

/* 颜色过渡 */
.theme-transition-colors {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* 交错动画 */
.theme-transition-stagger > * {
  transition-delay: calc(var(--theme-transition-delay) + var(--stagger-index, 0) * 50ms);
}

.theme-transition-stagger > *:nth-child(1) { --stagger-index: 0; }
.theme-transition-stagger > *:nth-child(2) { --stagger-index: 1; }
.theme-transition-stagger > *:nth-child(3) { --stagger-index: 2; }
.theme-transition-stagger > *:nth-child(4) { --stagger-index: 3; }
.theme-transition-stagger > *:nth-child(5) { --stagger-index: 4; }
.theme-transition-stagger > *:nth-child(6) { --stagger-index: 5; }
.theme-transition-stagger > *:nth-child(7) { --stagger-index: 6; }
.theme-transition-stagger > *:nth-child(8) { --stagger-index: 7; }
.theme-transition-stagger > *:nth-child(9) { --stagger-index: 8; }
.theme-transition-stagger > *:nth-child(10) { --stagger-index: 9; }

/* 性能优化 */
.theme-transition-optimized {
  /* 启用 GPU 加速 */
  will-change: transform, opacity, filter;
  
  /* 优化渲染 */
  contain: layout style paint;
  
  /* 减少重绘 */
  isolation: isolate;
}

/* 响应式适配 */
@media (max-width: 768px) {
  :root {
    --theme-transition-duration: 200ms;
  }
  
  .theme-transition-container {
    /* 移动端减少复杂效果 */
    filter: none;
  }
  
  .theme-transition-complex {
    /* 简化移动端动画 */
    transition: opacity var(--theme-transition-duration) var(--theme-transition-timing);
  }
}

/* 高性能设备优化 */
@media (min-resolution: 2dppx) {
  .theme-transition-container {
    /* 高分辨率设备优化 */
    transform: translateZ(0) scale(1.001);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --theme-transition-duration: 0ms;
    --theme-transition-delay: 0ms;
  }
  
  .theme-transition-container,
  .theme-transition-fade,
  .theme-transition-slide,
  .theme-transition-scale,
  .theme-transition-flip,
  .theme-transition-dissolve,
  .theme-transition-morph,
  .theme-transition-complex {
    transition: none !important;
    animation: none !important;
  }
  
  /* 保持基本的颜色过渡 */
  .theme-transition-colors {
    transition: 
      background-color 50ms ease,
      color 50ms ease,
      border-color 50ms ease;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .theme-transition-container {
    /* 高对比度模式下禁用模糊效果 */
    filter: none;
  }
  
  .theme-transition-dissolve {
    transition: opacity var(--theme-transition-duration) var(--theme-transition-timing);
  }
  
  .theme-transition-dissolve.transitioning {
    filter: none;
    opacity: var(--theme-transition-opacity);
  }
}

/* 暗色模式特定样式 */
.dark .theme-transition-container {
  /* 暗色模式下的优化 */
  --theme-transition-brightness: 0.95;
}

/* 亮色模式特定样式 */
.light .theme-transition-container {
  /* 亮色模式下的优化 */
  --theme-transition-brightness: 1.05;
}

/* 动画状态指示器 */
.theme-transition-indicator {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  padding: 0.5rem 1rem;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  font-size: 0.75rem;
  opacity: 0;
  transform: translateY(-1rem);
  transition: opacity 200ms ease, transform 200ms ease;
  pointer-events: none;
}

.theme-transition-indicator.active {
  opacity: 1;
  transform: translateY(0);
}

/* 进度条样式 */
.theme-transition-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: hsl(var(--primary));
  transform: scaleX(var(--theme-transition-progress));
  transform-origin: left;
  transition: transform 100ms ease;
  z-index: 9999;
  opacity: var(--theme-transition-active);
}

/* 调试模式样式 */
.theme-transition-debug {
  position: relative;
}

.theme-transition-debug::before {
  content: 'Transition: ' attr(data-transition-type);
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  font-size: 0.75rem;
  font-family: monospace;
  z-index: 10000;
  pointer-events: none;
}

/* 自定义缓动函数 */
.theme-transition-spring {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.theme-transition-bounce {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.theme-transition-elastic {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 组件特定过渡 */
.theme-transition-card {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing),
    transform var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-button {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

.theme-transition-text {
  transition: color var(--theme-transition-duration) var(--theme-transition-timing);
}

/* 全局应用过渡 */
body.theme-transitioning {
  /* 全局过渡状态 */
  --theme-transition-active: 1;
}

body.theme-transitioning * {
  /* 为所有元素应用颜色过渡 */
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing),
    fill var(--theme-transition-duration) var(--theme-transition-timing),
    stroke var(--theme-transition-duration) var(--theme-transition-timing);
}

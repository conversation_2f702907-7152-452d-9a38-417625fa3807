import { type ReactElement, memo } from 'react';

import {
  type ErrorMessageProps,
  type FormSubmitButtonProps,
  type RetryButtonProps,
  type StatusMessageProps,
} from './contact-form-types';

import { Button } from '@/components/ui/button';

/**
 * 表单提交按钮组件
 */
export const FormSubmitButton = memo(
  ({
    isSubmitting,
    isSubmittingWithRetry,
    memoizedT,
  }: FormSubmitButtonProps): ReactElement => {
    const isDisabled = isSubmitting || isSubmittingWithRetry;
    const buttonText = isDisabled
      ? memoizedT('submitting')
      : memoizedT('submit');

    return (
      <Button
        type='submit'
        disabled={isDisabled}
        className='w-full'
        aria-describedby={isDisabled ? 'submit-status' : undefined}
      >
        {buttonText}
        {isDisabled && (
          <span id='submit-status' className='sr-only'>
            正在提交表单
          </span>
        )}
      </Button>
    );
  }
);

FormSubmitButton.displayName = 'FormSubmitButton';

/**
 * 重试按钮组件
 */
export const RetryButton = memo(
  ({
    canRetry,
    submissionError,
    handleRetry,
  }: RetryButtonProps): ReactElement | null => {
    if (!canRetry || submissionError === null) {
      return null;
    }

    return (
      <Button
        type='button'
        variant='outline'
        onClick={() => void handleRetry()}
        className='w-full'
        aria-label='重试提交表单'
      >
        重试提交
      </Button>
    );
  }
);

RetryButton.displayName = 'RetryButton';

/**
 * 成功消息组件
 */
export const SuccessMessage = memo(
  ({ memoizedT }: StatusMessageProps): ReactElement => {
    return (
      <div
        role='alert'
        className='rounded-md bg-green-50 p-4 text-center text-sm text-green-800 dark:bg-green-900/20 dark:text-green-200'
        aria-live='polite'
      >
        {memoizedT('success')}
      </div>
    );
  }
);

SuccessMessage.displayName = 'SuccessMessage';

/**
 * 错误消息组件
 */
export const ErrorMessage = memo(
  ({ submissionError }: ErrorMessageProps): ReactElement | null => {
    if (submissionError === null) {
      return null;
    }

    return (
      <div
        role='alert'
        className='rounded-md bg-red-50 p-4 text-center text-sm text-red-800 dark:bg-red-900/20 dark:text-red-200'
        aria-live='polite'
      >
        {submissionError}
      </div>
    );
  }
);

ErrorMessage.displayName = 'ErrorMessage';

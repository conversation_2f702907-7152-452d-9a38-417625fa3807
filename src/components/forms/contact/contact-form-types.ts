import { type UseFormReturn } from 'react-hook-form';

import { type ContactFormData } from '@/lib/validations';





/**
 * 联系表单相关类型定义
 */ /**
 * 表单字段组件属性
 */
export interface ContactFormFieldsProps {
  form: UseFormReturn<ContactFormData>;
  t: (key: string) => string;
}
/**
 * 表单提交按钮属性
 */
export interface FormSubmitButtonProps {
  isSubmitting: boolean;
  isSubmittingWithRetry: boolean;
  memoizedT: (key: string) => string;
}
/**
 * 重试按钮属性
 */
export interface RetryButtonProps {
  canRetry: boolean;
  submissionError: string | null;
  handleRetry: () => Promise<void>;
}
/**
 * 状态消息组件属性
 */
export interface StatusMessageProps {
  memoizedT: (key: string) => string;
}
/**
 * 错误消息组件属性
 */
export interface ErrorMessageProps {
  submissionError: string | null;
}
/**
 * 表单状态类型
 */
export type FormStatus = 'idle' | 'success' | 'error';
/**
 * 表单逻辑Hook返回类型
 */
export interface ContactFormLogicReturn {
  form: UseFormReturn<ContactFormData>;
  submitStatus: FormStatus;
  isSubmitting: boolean;
  submissionError: string | null;
  canRetry: boolean;
  onSubmit: (data: ContactFormData) => Promise<void>;
  handleRetry: () => Promise<void>;
}
/**
 * 表单容器组件属性
 */
export interface ContactFormContainerProps {
  memoizedT: (key: string) => string;
}
/**
 * 通用表单字段属性
 */
export interface FormFieldWrapperProps {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
  htmlFor?: string;
}
/**
 * 必填标识组件属性
 */
export interface RequiredIndicatorProps {
  required?: boolean;
  'aria-label'?: string;
}

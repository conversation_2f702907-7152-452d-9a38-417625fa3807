import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useFormSubmission } from '@/hooks/use-form-submission';
import { contactFormSchema, type ContactFormData } from '@/lib/validations';

import { type ContactFormLogicReturn, type FormStatus } from './contact-form-types';

/**
 * 联系表单逻辑Hook
 * 
 * 功能：
 * - 表单状态管理
 * - 表单验证
 * - 表单提交处理
 * - 错误处理和重试逻辑
 */
export function useContactFormLogic(): ContactFormLogicReturn {
  const [submitStatus, setSubmitStatus] = useState<FormStatus>('idle');

  // 表单配置
  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      purpose: '',
      region: '',
      message: '',
    },
    mode: 'onBlur',
  });

  // 表单提交配置
  const formSubmissionConfig = useMemo(
    () => ({
      maxRetries: 3,
      retryDelay: 1000,
      showToast: true,
      onSuccess: () => setSubmitStatus('success'),
      onError: () => setSubmitStatus('error'),
    }),
    []
  );

  // 使用表单提交Hook
  const {
    isSubmitting,
    error: submissionError,
    canRetry,
    submitWithErrorHandling,
    retrySubmission,
    clearError,
  } = useFormSubmission(formSubmissionConfig);

  // 表单提交处理
  const onSubmit = useCallback(
    async (data: ContactFormData): Promise<void> => {
      await submitWithErrorHandling(async () => {
        // 模拟API调用
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 成功后重置表单
        form.reset();
        clearError();
      });
    },
    [submitWithErrorHandling, form, clearError]
  );

  // 重试处理
  const handleRetry = useCallback(async (): Promise<void> => {
    const currentValues = form.getValues();
    await retrySubmission(async () => {
      await onSubmit(currentValues);
    });
  }, [form, retrySubmission, onSubmit]);

  return {
    form,
    submitStatus,
    isSubmitting,
    submissionError,
    canRetry,
    onSubmit,
    handleRetry,
  };
}

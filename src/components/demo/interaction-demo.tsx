import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';

export function InteractionDemo(): ReactElement {
  const t = useTranslations('home.demo.interactionDemo');
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Input
            type="text"
            placeholder={t('placeholder')}
            className="w-full"
          />
        </div>
        <div className="flex gap-2">
          <Button variant="default">
            {t('buttons.primary')}
          </Button>
          <Button variant="outline">
            {t('buttons.secondary')}
          </Button>
        </div>
        <div className="p-4 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground">
            {t('feedback')}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

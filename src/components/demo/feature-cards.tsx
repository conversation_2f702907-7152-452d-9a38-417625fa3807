import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function FeatureCards(): ReactElement {
  const t = useTranslations('home.demo.featureCards');

  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
      <Card>
        <CardHeader>
          <CardTitle>{t('responsive.title')}</CardTitle>
          <CardDescription>{t('responsive.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground text-sm'>
            {t('responsive.content')}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('accessible.title')}</CardTitle>
          <CardDescription>{t('accessible.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground text-sm'>
            {t('accessible.content')}
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('performance.title')}</CardTitle>
          <CardDescription>{t('performance.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground text-sm'>
            {t('performance.content')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

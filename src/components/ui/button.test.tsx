import Link from 'next/link';
import { describe, expect, it, vi } from 'vitest';

import { fireEvent, render, screen } from '@/test/utils';

import { Button } from './button';






describe('Button 组件', () => {
  it('应该正确渲染基本按钮', () => {
    render(<Button>Click me</Button>);

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Click me');
    expect(button).toHaveAttribute('data-slot', 'button');
  });
  it('应该支持不同的变体', () => {
    const { rerender } = render(<Button variant='default'>Default</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary');
    rerender(<Button variant='secondary'>Secondary</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-secondary');
    rerender(<Button variant='outline'>Outline</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('border');
    rerender(<Button variant='ghost'>Ghost</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('hover:bg-accent');
    rerender(<Button variant='destructive'>Destructive</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
    rerender(<Button variant='link'>Link</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('underline-offset-4');
  });
  it('应该支持不同的尺寸', () => {
    const { rerender } = render(<Button size='default'>Default</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('h-9');
    rerender(<Button size='sm'>Small</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-8');
    rerender(<Button size='lg'>Large</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-10');
    rerender(<Button size='icon'>Icon</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('size-9');
  });
  it('应该支持禁用状态', () => {
    render(<Button disabled>Disabled</Button>);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:opacity-50');
  });
  it('应该支持自定义 className', () => {
    render(<Button className='custom-class'>Custom</Button>);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });
  it('应该支持点击事件', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  it('应该支持 asChild 属性', () => {
    render(
      <Button asChild>
        {' '}
        <Link href='/test'>Link Button</Link>{' '}
      </Button>
    );

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveTextContent('Link Button');
    expect(link).toHaveAttribute('href', '/test');
    expect(link).toHaveAttribute('data-slot', 'button');
  });
  it('应该传递其他 props', () => {
    render(
      <Button type='submit' data-testid='submit-btn'>
        {' '}
        Submit{' '}
      </Button>
    );

    const button = screen.getByTestId('submit-btn');
    expect(button).toHaveAttribute('type', 'submit');
  });
});

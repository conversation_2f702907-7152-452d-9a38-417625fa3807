'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import { ButtonDemo } from '@/components/demo/button-demo';
import { FeatureCards } from '@/components/demo/feature-cards';
import { InputDemo } from '@/components/demo/input-demo';
import { InteractionDemo } from '@/components/demo/interaction-demo';

/**
 * 组件演示区域
 * 整合所有 demo 组件，提供统一的展示界面
 */
export function DemoSection(): ReactElement {
  const t = useTranslations('home.demo');

  return (
    <section id="demo-section" className="py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </motion.div>

        {/* UI 组件展示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-semibold mb-8 text-center">
            {t('sections.ui.title')}
          </h3>
          <p className="text-muted-foreground text-center mb-8">
            {t('sections.ui.description')}
          </p>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ButtonDemo />
            <InputDemo />
          </div>
        </motion.div>

        {/* 功能演示 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-semibold mb-8 text-center">
            {t('sections.features.title')}
          </h3>
          <p className="text-muted-foreground text-center mb-8">
            {t('sections.features.description')}
          </p>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FeatureCards />
            <InteractionDemo />
          </div>
        </motion.div>
      </div>
    </section>
  );
}

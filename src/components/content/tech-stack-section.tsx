'use client';

import { motion } from 'framer-motion';
import { Code2, ExternalLink, Palette, Rocket, Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
 * 获取技术链接
 */
function getTechUrl(techId: string): string {
  const urls: Record<string, string> = {
    nextjs: 'https://nextjs.org',
    typescript: 'https://typescriptlang.org',
    react: 'https://react.dev',
    tailwind: 'https://tailwindcss.com',
    shadcn: 'https://ui.shadcn.com',
    framer: 'https://framer.com/motion',
    intl: 'https://next-intl-docs.vercel.app',
    eslint: 'https://eslint.org',
    prettier: 'https://prettier.io',
    vercel: 'https://vercel.com',
  };
  
  return urls[techId] ?? '#';
}

/**
 * 技术分类数据
 */
const categories = [
  {
    id: 'frontend',
    icon: Code2,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
    technologies: ['nextjs', 'typescript', 'react'],
  },
  {
    id: 'styling',
    icon: Palette,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
    technologies: ['tailwind', 'shadcn', 'framer'],
  },
  {
    id: 'tooling',
    icon: Settings,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
    technologies: ['intl', 'eslint', 'prettier'],
  },
  {
    id: 'deployment',
    icon: Rocket,
    color: 'text-orange-500',
    bgColor: 'bg-orange-500/10',
    technologies: ['vercel'],
  },
];

/**
 * 动画配置
 */
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

/**
 * 技术栈展示组件
 */
export function TechStackSection(): React.JSX.Element {
  const t = useTranslations('home.techStack');

  return (
    <section className="py-20 md:py-32 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('description')}
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <motion.div key={category.id} variants={itemVariants}>
                <Card className="h-full">
                  <CardHeader>
                    <div
                      className={`w-12 h-12 rounded-lg ${category.bgColor} flex items-center justify-center mb-4`}
                    >
                      <IconComponent className={`w-6 h-6 ${category.color}`} />
                    </div>
                    <CardTitle>{t(`categories.${category.id}.title`)}</CardTitle>
                    <CardDescription>
                      {t(`categories.${category.id}.description`)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {category.technologies.map((tech) => (
                        <a
                          key={tech}
                          href={getTechUrl(tech)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50 transition-colors group"
                        >
                          <span className="text-sm font-medium">
                            {t(`technologies.${tech}`)}
                          </span>
                          <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </a>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}

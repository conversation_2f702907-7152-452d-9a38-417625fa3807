'use client';

import { motion } from 'framer-motion';
import { ArrowRight, Github } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';

/**
 * 动画配置
 */
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

/**
 * 英雄区域组件
 */
export function HeroSection(): React.JSX.Element {
  const t = useTranslations('home.hero');

  const handleGetStarted = (): void => {
    const demoSection = document.getElementById('demo-section');
    if (demoSection !== null) {
      demoSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleViewGithub = (): void => {
    window.open(
      'https://github.com/tucsenberg/tucsenberg-web-nextjs15',
      '_blank'
    );
  };

  return (
    <section className='from-background via-background to-muted/20 relative flex min-h-screen items-center justify-center bg-gradient-to-br'>
      <div className='container mx-auto px-4 text-center'>
        <motion.div
          variants={containerVariants}
          initial='hidden'
          animate='visible'
          className='mx-auto max-w-4xl'
        >
          <motion.h1
            variants={itemVariants}
            className='from-foreground to-foreground/70 mb-6 bg-gradient-to-r bg-clip-text text-4xl font-bold text-transparent md:text-6xl lg:text-7xl'
          >
            {t('title')}
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className='text-muted-foreground mx-auto mb-8 max-w-2xl text-lg md:text-xl'
          >
            {t('description')}
          </motion.p>

          <motion.div
            variants={itemVariants}
            className='flex flex-col items-center justify-center gap-4 sm:flex-row'
          >
            <Button size='lg' onClick={handleGetStarted} className='group'>
              {t('cta.primary')}
              <ArrowRight className='ml-2 h-4 w-4 transition-transform group-hover:translate-x-1' />
            </Button>

            <Button
              variant='outline'
              size='lg'
              onClick={handleViewGithub}
              className='group'
            >
              <Github className='mr-2 h-4 w-4' />
              {t('cta.secondary')}
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}

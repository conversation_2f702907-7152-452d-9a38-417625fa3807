'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';

/**
 * 特性展示组件
 */
export function FeaturesSection(): React.JSX.Element {
  const t = useTranslations('home.features');

  return (
    <section className='py-20 md:py-32'>
      <div className='container mx-auto px-4'>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className='mb-16 text-center'
        >
          <h2 className='mb-4 text-3xl font-bold md:text-4xl'>{t('title')}</h2>
          <p className='text-muted-foreground mx-auto max-w-2xl text-lg'>
            {t('description')}
          </p>
        </motion.div>
      </div>
    </section>
  );
}

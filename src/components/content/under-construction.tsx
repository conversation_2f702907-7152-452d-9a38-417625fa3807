'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Hammer } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  AnimationContainer,
  ScrollReveal,
} from '@/components/ui/scroll-reveal';
import { Link } from '@/i18n/routing';
import { presetAnimations } from '@/lib/animations';

/**
 * Tailwind CSS 动画类名
 */
const animationClasses = {
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  ping: 'animate-ping',
} as const;

/**
 * 施工中页面组件
 */
export function UnderConstruction(): React.JSX.Element {
  const t = useTranslations('pages.underConstruction');

  return (
    <div className='from-background via-background to-muted/20 flex min-h-screen items-center justify-center bg-gradient-to-br'>
      <div className='container mx-auto px-4 text-center'>
        <AnimationContainer animation={presetAnimations.fadeInUp}>
          <div className='mx-auto max-w-2xl'>
            <ScrollReveal>
              <div className='mb-8'>
                <div className='relative inline-block'>
                  <Construction className='text-primary mx-auto mb-4 h-24 w-24' />
                  <Hammer
                    className={`text-muted-foreground absolute -top-2 -right-2 h-8 w-8 ${animationClasses.bounce}`}
                  />
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.2}>
              <h1 className='from-foreground to-foreground/70 mb-6 bg-gradient-to-r bg-clip-text text-4xl font-bold text-transparent md:text-6xl'>
                {t('title')}
              </h1>
            </ScrollReveal>

            <ScrollReveal delay={0.4}>
              <p className='text-muted-foreground mx-auto mb-8 max-w-lg text-lg md:text-xl'>
                {t('description')}
              </p>
            </ScrollReveal>

            <ScrollReveal delay={0.6}>
              <div className='flex flex-col items-center justify-center gap-4 sm:flex-row'>
                <Button asChild size='lg'>
                  <Link href='/'>
                    <ArrowLeft className='mr-2 h-4 w-4' />
                    {t('backToHome')}
                  </Link>
                </Button>
              </div>
            </ScrollReveal>
          </div>
        </AnimationContainer>
      </div>
    </div>
  );
}

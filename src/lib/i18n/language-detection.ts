/**
 * 语言检测和管理工具
 *
 * 功能：
 * - 支持的语言类型定义
 * - 语言检测逻辑
 * - 语言切换功能
 * - 浏览器语言偏好检测
 */

/**
 * 支持的语言类型
 */
export type SupportedLocale = 'en' | 'zh';

/**
 * 语言配置接口
 */
export interface LanguageConfig {
  code: SupportedLocale;
  name: string;
  nativeName: string;
  flag: string;
  direction: 'ltr' | 'rtl';
}

/**
 * 支持的语言列表
 */
export const supportedLocales: SupportedLocale[] = ['en', 'zh'];

/**
 * 语言配置映射
 */
export const languageConfigs: Record<SupportedLocale, LanguageConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    direction: 'ltr',
  },
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    direction: 'ltr',
  },
};

/**
 * 默认语言
 */
export const defaultLocale: SupportedLocale = 'en';

/**
 * 检查是否为支持的语言
 */
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return supportedLocales.includes(locale as SupportedLocale);
}

/**
 * 获取浏览器首选语言
 */
export function getBrowserLocale(): SupportedLocale {
  if (typeof window === 'undefined') {
    return defaultLocale;
  }

  const browserLang = navigator.language.split('-')[0];
  return isSupportedLocale(browserLang) ? browserLang : defaultLocale;
}

/**
 * 获取语言配置
 */
export function getLanguageConfig(locale: SupportedLocale): LanguageConfig {
  return languageConfigs[locale];
}

/**
 * 获取语言显示名称
 */
export function getLanguageDisplayName(locale: SupportedLocale): string {
  return languageConfigs[locale].nativeName;
}

/**
 * 语言检测结果接口
 */
export interface LanguageDetectionResult {
  locale: SupportedLocale;
  source: 'url' | 'cookie' | 'header' | 'browser' | 'default';
  confidence: number;
}

/**
 * 从 URL 路径检测语言
 */
export function detectLocaleFromPath(pathname: string): SupportedLocale | null {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];

  if (firstSegment !== undefined && isSupportedLocale(firstSegment)) {
    return firstSegment;
  }

  return null;
}

/**
 * 智能语言检测
 */
export function detectLanguage(
  pathname?: string,
  acceptLanguage?: string,
  cookieLocale?: string
): LanguageDetectionResult {
  // 1. 从 URL 路径检测
  if (pathname !== undefined) {
    const pathLocale = detectLocaleFromPath(pathname);
    if (pathLocale !== null) {
      return {
        locale: pathLocale,
        source: 'url',
        confidence: 1.0,
      };
    }
  }

  // 2. 从 Cookie 检测
  if (cookieLocale !== undefined && isSupportedLocale(cookieLocale)) {
    return {
      locale: cookieLocale,
      source: 'cookie',
      confidence: 0.9,
    };
  }

  // 3. 从 Accept-Language 头检测
  if (acceptLanguage !== undefined) {
    const headerLang = acceptLanguage.split(',')[0]?.split('-')[0];
    if (headerLang !== undefined && isSupportedLocale(headerLang)) {
      return {
        locale: headerLang,
        source: 'header',
        confidence: 0.7,
      };
    }
  }

  // 4. 从浏览器检测
  const browserLocale = getBrowserLocale();
  if (browserLocale !== defaultLocale) {
    return {
      locale: browserLocale,
      source: 'browser',
      confidence: 0.5,
    };
  }

  // 5. 默认语言
  return {
    locale: defaultLocale,
    source: 'default',
    confidence: 0.1,
  };
}

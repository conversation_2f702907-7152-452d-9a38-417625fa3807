/**
 * 动态翻译加载器
 *
 * 功能：
 * - 按需加载翻译模块
 * - 翻译缓存管理
 * - 错误处理和回退
 * - 性能优化
 */
import { type SupportedLocale } from './language-detection';

/**
 * 翻译模块类型
 */
export type TranslationModule =
  | 'common'
  | 'navigation'
  | 'home'
  | 'about'
  | 'contact'
  | 'blog'
  | 'products'
  | 'services'
  | 'forms'
  | 'errors';

/**
 * 翻译数据接口
 */
export interface TranslationData {
  [key: string]: string | TranslationData;
}

/**
 * 翻译模块配置
 */
export interface TranslationModuleConfig {
  module: TranslationModule;
  priority: number;
  preload: boolean;
  fallback?: TranslationModule;
}

/**
 * 翻译加载结果
 */
export interface TranslationLoadResult {
  data: TranslationData;
  module: TranslationModule;
  locale: SupportedLocale;
  loadTime: number;
  cached: boolean;
}

/**
 * 翻译缓存接口
 */
interface TranslationCache {
  [locale: string]: {
    [module: string]: {
      data: TranslationData;
      timestamp: number;
      expires: number;
    };
  };
}

/**
 * 翻译加载器类
 */
export class DynamicTranslationLoader {
  private cache: TranslationCache = {};
  private readonly cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  private readonly baseUrl = '/api/translations';

  /**
   * 加载翻译模块
   */
  async loadModule(
    locale: SupportedLocale,
    module: TranslationModule
  ): Promise<TranslationLoadResult> {
    const startTime = Date.now();

    // 检查缓存
    const cached = this.getCachedTranslation(locale, module);
    if (cached !== null) {
      return {
        data: cached,
        module,
        locale,
        loadTime: Date.now() - startTime,
        cached: true,
      };
    }

    try {
      // 从 API 加载
      const response = await fetch(`${this.baseUrl}/${locale}/${module}`);

      if (!response.ok) {
        throw new Error(`Failed to load translation: ${response.status}`);
      }

      const data = (await response.json()) as TranslationData;

      // 缓存翻译数据
      this.cacheTranslation(locale, module, data);

      return {
        data,
        module,
        locale,
        loadTime: Date.now() - startTime,
        cached: false,
      };
    } catch (error) {
      console.error(
        `Failed to load translation module ${module} for ${locale}:`,
        error
      );

      // 返回空的翻译数据作为回退
      return {
        data: {},
        module,
        locale,
        loadTime: Date.now() - startTime,
        cached: false,
      };
    }
  }

  /**
   * 批量加载翻译模块
   */
  async loadModules(
    locale: SupportedLocale,
    modules: TranslationModule[]
  ): Promise<TranslationLoadResult[]> {
    const promises = modules.map((module) => this.loadModule(locale, module));
    return Promise.all(promises);
  }

  /**
   * 预加载翻译模块
   */
  preloadModules(locale: SupportedLocale, modules: TranslationModule[]): void {
    // 并行预加载，但不等待结果
    modules.forEach((module) => {
      void this.loadModule(locale, module);
    });
  }

  /**
   * 获取缓存的翻译
   */
  private getCachedTranslation(
    locale: SupportedLocale,
    module: TranslationModule
  ): TranslationData | null {
    const localeCache = this.cache[locale];
    if (localeCache === undefined) {
      return null;
    }

    const moduleCache = localeCache[module];
    if (moduleCache === undefined) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > moduleCache.expires) {
      delete localeCache[module];
      return null;
    }

    return moduleCache.data;
  }

  /**
   * 缓存翻译数据
   */
  private cacheTranslation(
    locale: SupportedLocale,
    module: TranslationModule,
    data: TranslationData
  ): void {
    this.cache[locale] ??= {};

    this.cache[locale][module] = {
      data,
      timestamp: Date.now(),
      expires: Date.now() + this.cacheTimeout,
    };
  }

  /**
   * 清除缓存
   */
  clearCache(locale?: SupportedLocale, module?: TranslationModule): void {
    if (locale !== undefined && module !== undefined) {
      // 清除特定模块缓存
      if (this.cache[locale] !== undefined) {
        delete this.cache[locale][module];
      }
    } else if (locale !== undefined) {
      // 清除特定语言缓存
      delete this.cache[locale];
    } else {
      // 清除所有缓存
      this.cache = {};
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    totalEntries: number;
    locales: string[];
    modules: Record<string, string[]>;
  } {
    const locales = Object.keys(this.cache);
    const modules: Record<string, string[]> = {};
    let totalEntries = 0;

    locales.forEach((locale) => {
      const moduleKeys = Object.keys(this.cache[locale]);
      modules[locale] = moduleKeys;
      totalEntries += moduleKeys.length;
    });

    return {
      totalEntries,
      locales,
      modules,
    };
  }
}

/**
 * 默认翻译加载器实例
 */
export const translationLoader = new DynamicTranslationLoader();

/**
 * 支持的翻译模块列表
 */
export const supportedModules: TranslationModule[] = [
  'common',
  'navigation',
  'home',
  'about',
  'contact',
  'blog',
  'products',
  'services',
  'forms',
  'errors',
];

/**
 * 模块配置
 */
export const moduleConfigs: Record<TranslationModule, TranslationModuleConfig> =
  {
    common: { module: 'common', priority: 1, preload: true },
    navigation: { module: 'navigation', priority: 2, preload: true },
    home: { module: 'home', priority: 3, preload: true },
    about: { module: 'about', priority: 4, preload: false },
    contact: { module: 'contact', priority: 5, preload: false },
    blog: { module: 'blog', priority: 6, preload: false },
    products: { module: 'products', priority: 7, preload: false },
    services: { module: 'services', priority: 8, preload: false },
    forms: { module: 'forms', priority: 9, preload: false, fallback: 'common' },
    errors: {
      module: 'errors',
      priority: 10,
      preload: false,
      fallback: 'common',
    },
  };

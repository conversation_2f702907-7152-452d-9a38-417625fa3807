/**
 * 动画预设配置
 * 
 * 提供常用的动画效果配置，用于组件的动画展示
 */

export interface AnimationConfig {
  initial: Record<string, unknown>;
  animate: Record<string, unknown>;
  transition?: Record<string, unknown>;
}

/**
 * 预设动画配置
 */
export const presetAnimations = {
  /**
   * 淡入向上动画
   */
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 淡入向下动画
   */
  fadeInDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 淡入向左动画
   */
  fadeInLeft: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.6, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 淡入向右动画
   */
  fadeInRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.6, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 缩放淡入动画
   */
  scaleIn: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.5, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 旋转淡入动画
   */
  rotateIn: {
    initial: { opacity: 0, rotate: -10 },
    animate: { opacity: 1, rotate: 0 },
    transition: { duration: 0.7, ease: 'easeOut' },
  } as AnimationConfig,

  /**
   * 弹跳动画
   */
  bounce: {
    initial: { opacity: 0, y: -30 },
    animate: { opacity: 1, y: 0 },
    transition: { 
      duration: 0.8, 
      ease: 'easeOut',
      type: 'spring',
      bounce: 0.4,
    },
  } as AnimationConfig,

  /**
   * 滑入动画
   */
  slideIn: {
    initial: { opacity: 0, x: -100 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.6, ease: 'easeOut' },
  } as AnimationConfig,
} as const;

/**
 * 动画持续时间常量
 */
export const animationDurations = {
  fast: 0.3,
  normal: 0.6,
  slow: 1.0,
} as const;

/**
 * 动画缓动函数
 */
export const animationEasing = {
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
} as const;

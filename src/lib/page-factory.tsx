import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 页面工厂函数
 * 
 * 功能：
 * - 创建标准化的页面组件
 * - 生成 SEO metadata
 * - 支持多语言
 * - 减少代码重复
 */

/**
 * 页面参数接口
 */
interface PageParams {
  params: Promise<{ locale: string }>;
}

/**
 * 页面工厂返回类型
 */
interface PageFactoryResult {
  generateMetadata: (props: PageParams) => Promise<Metadata>;
  PageComponent: (props: PageParams) => React.JSX.Element;
}

/**
 * 创建建设中页面
 */
export function createUnderConstructionPage(
  translationKey: string
): PageFactoryResult {
  /**
   * 生成页面 metadata
   */
  const generateMetadata = async ({ params }: PageParams): Promise<Metadata> => {
    const { locale } = await params;
    const t = await getTranslations({ locale, namespace: translationKey });

    return {
      title: t('meta.title'),
      description: t('meta.description'),
      keywords: t('meta.keywords'),
      openGraph: {
        title: t('meta.title'),
        description: t('meta.description'),
        type: 'website',
        locale,
      },
      twitter: {
        card: 'summary_large_image',
        title: t('meta.title'),
        description: t('meta.description'),
      },
      alternates: {
        canonical: `/${locale}/${translationKey.replace('pages.', '')}`,
        languages: {
          'en': `/en/${translationKey.replace('pages.', '')}`,
          'zh': `/zh/${translationKey.replace('pages.', '')}`,
        },
      },
    };
  };

  /**
   * 页面组件
   */
  const PageComponent = ({ params: _params }: PageParams): React.JSX.Element => {
    return <UnderConstruction />;
  };

  return {
    generateMetadata,
    PageComponent,
  };
}

/**
 * 创建标准页面
 */
export function createStandardPage(
  translationKey: string,
  Component: React.ComponentType
): PageFactoryResult {
  /**
   * 生成页面 metadata
   */
  const generateMetadata = async ({ params }: PageParams): Promise<Metadata> => {
    const { locale } = await params;
    const t = await getTranslations({ locale, namespace: translationKey });

    return {
      title: t('meta.title'),
      description: t('meta.description'),
      keywords: t('meta.keywords'),
      openGraph: {
        title: t('meta.title'),
        description: t('meta.description'),
        type: 'website',
        locale,
      },
      twitter: {
        card: 'summary_large_image',
        title: t('meta.title'),
        description: t('meta.description'),
      },
      alternates: {
        canonical: `/${locale}/${translationKey.replace('pages.', '')}`,
        languages: {
          'en': `/en/${translationKey.replace('pages.', '')}`,
          'zh': `/zh/${translationKey.replace('pages.', '')}`,
        },
      },
    };
  };

  /**
   * 页面组件
   */
  const PageComponent = ({ params: _params }: PageParams): React.JSX.Element => {
    return <Component />;
  };

  return {
    generateMetadata,
    PageComponent,
  };
}

/**
 * 创建带布局的页面
 */
export function createLayoutPage(
  translationKey: string,
  Component: React.ComponentType,
  Layout: React.ComponentType<{ children: React.ReactNode }>
): PageFactoryResult {
  /**
   * 生成页面 metadata
   */
  const generateMetadata = async ({ params }: PageParams): Promise<Metadata> => {
    const { locale } = await params;
    const t = await getTranslations({ locale, namespace: translationKey });

    return {
      title: t('meta.title'),
      description: t('meta.description'),
      keywords: t('meta.keywords'),
      openGraph: {
        title: t('meta.title'),
        description: t('meta.description'),
        type: 'website',
        locale,
      },
      twitter: {
        card: 'summary_large_image',
        title: t('meta.title'),
        description: t('meta.description'),
      },
      alternates: {
        canonical: `/${locale}/${translationKey.replace('pages.', '')}`,
        languages: {
          'en': `/en/${translationKey.replace('pages.', '')}`,
          'zh': `/zh/${translationKey.replace('pages.', '')}`,
        },
      },
    };
  };

  /**
   * 页面组件
   */
  const PageComponent = ({ params: _params }: PageParams): React.JSX.Element => {
    return (
      <Layout>
        <Component />
      </Layout>
    );
  };

  return {
    generateMetadata,
    PageComponent,
  };
}

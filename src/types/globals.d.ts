/**
 * 全局类型声明文件
 * 企业级 TypeScript 严格模式支持
 */

// CSS 模块类型声明
declare module '*.css' {
  const content: string;
  export default content;
}

declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.sass' {
  const content: { [className: string]: string };
  export default content;
}

// 图片资源类型声明
declare module '*.svg' {
  const content: string;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.webp' {
  const content: string;
  export default content;
}

declare module '*.ico' {
  const content: string;
  export default content;
}

// 环境变量类型声明
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      readonly NODE_ENV: 'development' | 'production' | 'test';
      readonly NEXT_PUBLIC_APP_URL?: string;
      readonly NEXT_PUBLIC_APP_NAME?: string;
      readonly NEXT_PUBLIC_APP_VERSION?: string;
    }
  }
}

// 全局类型扩展
declare global {
  // 可以在这里添加全局类型扩展
  // 注意：console 在开发环境中仍然可用，但 ESLint 会阻止其在生产代码中使用
}

export {};

{"automatedChecks": {"tools": ["TypeScript", "ESLint", "<PERSON>ttier", "Next.js Build", "Security Audit", "Performance Monitor"], "scope": ["代码质量", "类型检查", "格式化", "构建验证", "安全检查", "性能监控"], "threshold": "100%通过率", "commands": ["pnpm type-check", "pnpm lint:check", "pnpm format:check", "pnpm build", "pnpm security:check", "pnpm perf:check"], "preCommitValidation": "基于 lefthook.yml 配置", "parallelExecution": {"group1": ["pnpm type-check", "pnpm format:check"], "group2": ["pnpm lint:check"], "group3": ["pnpm build"], "group4": ["pnpm security:check"], "group5": ["pnpm perf:check"], "estimatedTime": "60-90秒（并行执行）"}}, "securityChecks": {"tools": ["eslint-plugin-security", "pnpm audit", "自定义安全扫描"], "scope": ["代码安全", "依赖安全", "敏感信息检查"], "threshold": "零漏洞政策", "commands": ["pnpm security:scan", "pnpm security:deps", "pnpm security:audit"], "severityLevels": {"critical": 0, "high": 0, "moderate": 5, "low": 10}, "automatedFix": "pnpm audit --fix", "reportFormat": "详细报告包含文件位置和修复建议"}, "performanceMonitoring": {"tools": ["Lighthouse CI", "Web Vitals", "Bundle Analyzer"], "scope": ["Core Web Vitals", "Lighthouse分数", "包大小分析"], "threshold": "企业级性能标准", "commands": ["pnpm lighthouse:ci", "pnpm perf:analyze", "pnpm perf:monitor"], "lighthouseThresholds": {"performance": 90, "accessibility": 95, "bestPractices": 90, "seo": 90, "pwa": 80}, "webVitalsThresholds": {"LCP": 2500, "FID": 100, "CLS": 0.1, "FCP": 1800, "TTFB": 800}, "bundleSizeThresholds": {"maxInitialBundle": "200KB", "maxTotalBundle": "1MB"}, "reportFormat": "HTML + JSON 性能报告，包含优化建议"}, "qualityStandards": {"typescript": {"strictMode": true, "noEmitOnError": true}, "eslint": {"maxWarnings": 0, "enforceComplexity": true}, "prettier": {"enforceFormatting": true}, "build": {"mustSucceed": true, "checkOptimization": true}}, "emergencySkip": {"enabled": true, "requiresApproval": true, "logRequired": true}}
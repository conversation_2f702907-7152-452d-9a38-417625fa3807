{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^(next/(.*)$)|^(next$)", "^(react/(.*)$)|^(react$)", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": false, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "tailwindConfig": "./tailwind.config.ts", "tailwindFunctions": ["clsx", "cn", "cva"]}
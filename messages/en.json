{"common": {"title": "Tucsenberg Web", "description": "Modern B2B Enterprise Website Template", "loading": "Loading...", "error": "An error occurred", "retry": "Retry", "close": "Close", "save": "Save", "cancel": "Cancel", "submit": "Submit", "next": "Next", "previous": "Previous"}, "navigation": {"home": "Home", "products": "Products", "blog": "Blog", "about": "About", "services": "Services", "contact": "Contact", "language": "Language", "theme": "Theme", "menu": "<PERSON><PERSON>", "close": "Close"}, "home": {"hero": {"title": "Tucsenberg Web", "subtitle": "Modern B2B Enterprise Website Template", "description": "Built with Next.js 15, TypeScript, Tailwind CSS, and shadcn/ui. A comprehensive template showcasing modern web development best practices.", "cta": {"primary": "View Components", "secondary": "View Source Code"}}, "techStack": {"title": "Technology Stack", "subtitle": "Built with cutting-edge technologies for optimal performance and developer experience", "categories": {"frontend": {"title": "Frontend", "description": "Modern React ecosystem with type safety"}, "styling": {"title": "Styl<PERSON>", "description": "Utility-first CSS with component library"}, "tooling": {"title": "Development", "description": "Enhanced developer experience and quality"}, "deployment": {"title": "Deployment", "description": "Production-ready hosting and optimization"}}, "technologies": {"nextjs": {"name": "Next.js 15", "description": "React framework with App Router and Turbopack"}, "typescript": {"name": "TypeScript", "description": "Type-safe JavaScript with enhanced IDE support"}, "react": {"name": "React 18", "description": "Modern React with concurrent features"}, "tailwind": {"name": "Tailwind CSS", "description": "Utility-first CSS framework"}, "shadcn": {"name": "shadcn/ui", "description": "Beautiful and accessible component library"}, "framer": {"name": "Framer Motion", "description": "Production-ready motion library for React"}, "intl": {"name": "next-intl", "description": "Internationalization for Next.js applications"}, "eslint": {"name": "ESLint", "description": "Code linting with enterprise-grade rules"}, "prettier": {"name": "<PERSON>ttier", "description": "Code formatting for consistent style"}, "vercel": {"name": "Vercel", "description": "Optimized deployment platform"}}}, "demo": {"title": "Component Showcase", "subtitle": "Interactive demonstrations of our component library and features", "sections": {"ui": {"title": "UI Components", "description": "shadcn/ui components with custom styling", "buttonComponents": "Button Components", "inputComponents": "Input Components"}, "features": {"title": "Feature Demos", "description": "Interactive feature demonstrations", "featureCards": "Feature Cards", "interactiveElements": "Interactive Elements"}, "config": {"title": "Configuration", "description": "Theme and language switching capabilities"}}, "featureCards": {"features": {"title": "Features", "description": "Core features of shadcn/ui", "items": {"newYork": "New York modern style", "typescript": "TypeScript support", "darkMode": "Dark mode support", "customizable": "Fully customizable", "accessibility": "Accessibility ready"}}, "techStack": {"title": "Tech Stack", "description": "Core technologies used", "items": {"radix": "Radix UI Primitives", "tailwind": "Tailwind CSS", "cva": "class-variance-authority", "clsx": "clsx + tailwind-merge", "lucide": "Lucide React Icons"}}, "installation": {"title": "Installation Status", "description": "Component library installation verification", "items": {"shadcn": "shadcn/ui", "newYorkStyle": "New York style", "baseComponents": "Base components"}, "status": {"installed": "Installed", "configured": "Configured"}}}, "interactionDemo": {"title": "Interaction Test", "description": "Test component interactions and state changes", "buttons": {"click": "Click test button interaction", "hover": "Hover effect test", "focus": "Focus state test"}, "inputs": {"focusPlaceholder": "Test input focus effect", "hoverPlaceholder": "Test input hover effect"}}}, "features": {"title": "Key Features", "subtitle": "Everything you need for modern enterprise web development", "cta": {"title": "Ready to get started?", "description": "Explore our comprehensive component library and start building your next project with confidence."}, "items": {"responsive": {"title": "Responsive Design", "description": "Mobile-first approach with seamless desktop experience"}, "accessible": {"title": "Accessibility", "description": "WCAG compliant components with keyboard navigation"}, "performance": {"title": "Performance", "description": "Optimized for Core Web Vitals and fast loading"}, "seo": {"title": "SEO Ready", "description": "Built-in SEO optimization and meta tag management"}, "i18n": {"title": "Internationalization", "description": "Multi-language support with easy content management"}, "darkMode": {"title": "Dark Mode", "description": "System-aware theme switching with smooth transitions"}}}}, "footer": {"company": {"name": "Tucsenberg Web", "description": "Modern B2B Enterprise Website Template", "address": "123 Business Street, Enterprise City, EC 12345"}, "products": {"title": "Products", "webDevelopment": "Web Development", "mobileApps": "Mobile Apps", "consulting": "Consulting", "support": "Support"}, "support": {"title": "Support", "documentation": "Documentation", "helpCenter": "Help Center", "community": "Community", "contact": "Contact Us"}, "contact": {"title": "Contact", "email": "<EMAIL>", "phone": "+****************", "address": "Visit Us"}, "social": {"title": "Follow Us", "twitter": "Twitter", "linkedin": "LinkedIn", "github": "GitHub", "youtube": "YouTube"}, "legal": {"copyright": "© 2024 Tucsenberg Web. All rights reserved.", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}}, "underConstruction": {"title": "Under Construction", "subtitle": "We're working hard to bring you something amazing", "description": "This page is currently under development. We're crafting an exceptional experience that will be worth the wait.", "backToHome": "Back to Home", "comingSoon": "Coming Soon"}, "pages": {"products": {"title": "Products - Tucsenberg Web", "description": "Discover our innovative products and services designed to help your business grow and succeed in the digital world."}, "blog": {"title": "Blog - Tucsenberg Web", "description": "Read our latest insights, tutorials, and industry news about web development, technology trends, and digital innovation."}, "about": {"title": "About Us - Tu<PERSON>enberg Web", "description": "Learn about our company, our mission, our team, and our commitment to delivering exceptional web solutions."}, "contact": {"title": "Contact Us - Tucsenberg Web", "description": "Get in touch with us to learn how we can help your business achieve digital transformation and growth goals.", "form": {"title": "Contact Us", "subtitle": "We'd love to hear from you", "fields": {"name": {"label": "Name", "placeholder": "Enter your full name", "required": "Name is required"}, "email": {"label": "Email", "placeholder": "Enter your email address", "required": "Email is required", "invalid": "Please enter a valid email address"}, "purpose": {"label": "Inquiry Type", "placeholder": "Select inquiry type", "options": {"purchase": "Product Purchase", "partnership": "Partnership", "other": "Other Inquiry"}}, "region": {"label": "Region", "placeholder": "Enter your region (optional)"}, "message": {"label": "Additional Information", "placeholder": "Tell us more about your needs (optional)"}}, "submit": "Send Message", "submitting": "Sending...", "success": "Message sent successfully! We'll get back to you soon.", "error": "Failed to send message. Please try again later."}}}, "theme": {"light": "Light", "dark": "Dark", "system": "System"}}
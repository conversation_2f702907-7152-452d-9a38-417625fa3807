{"common": {"title": "Tucsenberg Web", "description": "现代化 B2B 企业网站模板", "loading": "加载中...", "error": "发生错误", "retry": "重试", "close": "关闭", "save": "保存", "cancel": "取消", "submit": "提交", "next": "下一步", "previous": "上一步"}, "navigation": {"home": "首页", "products": "产品", "blog": "博客", "about": "关于我们", "services": "服务", "contact": "联系我们", "language": "语言", "theme": "主题", "menu": "菜单", "close": "关闭"}, "home": {"hero": {"title": "Tucsenberg Web", "subtitle": "现代化 B2B 企业网站模板", "description": "基于 Next.js 15、TypeScript、Tailwind CSS 和 shadcn/ui 构建。展示现代网站开发最佳实践的综合模板。", "cta": {"primary": "查看组件", "secondary": "查看源码"}}, "techStack": {"title": "技术栈", "subtitle": "采用前沿技术构建，确保最佳性能和开发体验", "categories": {"frontend": {"title": "前端框架", "description": "现代 React 生态系统，类型安全"}, "styling": {"title": "样式系统", "description": "实用优先的 CSS 和组件库"}, "tooling": {"title": "开发工具", "description": "增强开发体验和代码质量"}, "deployment": {"title": "部署平台", "description": "生产就绪的托管和优化"}}, "technologies": {"nextjs": {"name": "Next.js 15", "description": "具有 App Router 和 Turbopack 的 React 框架"}, "typescript": {"name": "TypeScript", "description": "类型安全的 JavaScript，增强 IDE 支持"}, "react": {"name": "React 18", "description": "具有并发特性的现代 React"}, "tailwind": {"name": "Tailwind CSS", "description": "实用优先的 CSS 框架"}, "shadcn": {"name": "shadcn/ui", "description": "美观且易于访问的组件库"}, "framer": {"name": "Framer Motion", "description": "React 的生产就绪动画库"}, "intl": {"name": "next-intl", "description": "Next.js 应用的国际化解决方案"}, "eslint": {"name": "ESLint", "description": "企业级代码检查规则"}, "prettier": {"name": "<PERSON>ttier", "description": "一致代码风格的格式化工具"}, "vercel": {"name": "Vercel", "description": "优化的部署平台"}}}, "demo": {"title": "组件展示", "subtitle": "组件库和功能的交互式演示", "sections": {"ui": {"title": "UI 组件", "description": "带有自定义样式的 shadcn/ui 组件", "buttonComponents": "按钮组件", "inputComponents": "输入组件"}, "features": {"title": "功能演示", "description": "交互式功能演示", "featureCards": "功能卡片", "interactiveElements": "交互元素"}, "config": {"title": "配置选项", "description": "主题和语言切换功能"}}, "featureCards": {"features": {"title": "功能特性", "description": "shadcn/ui 的核心特性", "items": {"newYork": "New York 现代风格", "typescript": "TypeScript 支持", "darkMode": "暗色模式支持", "customizable": "完全可定制", "accessibility": "无障碍访问"}}, "techStack": {"title": "技术栈", "description": "使用的核心技术", "items": {"radix": "Radix UI Primitives", "tailwind": "Tailwind CSS", "cva": "class-variance-authority", "clsx": "clsx + tailwind-merge", "lucide": "Lucide React Icons"}}, "installation": {"title": "安装状态", "description": "组件库安装验证", "items": {"shadcn": "shadcn/ui", "newYorkStyle": "New York 风格", "baseComponents": "基础组件"}, "status": {"installed": "已安装", "configured": "已配置"}}}, "interactionDemo": {"title": "交互测试", "description": "测试组件的交互效果和状态变化", "buttons": {"click": "点击测试按钮交互", "hover": "Hover 效果测试", "focus": "Focus 状态测试"}, "inputs": {"focusPlaceholder": "测试输入框 focus 效果", "hoverPlaceholder": "测试输入框 hover 效果"}}}, "features": {"title": "主要特性", "subtitle": "现代企业网站开发所需的一切", "cta": {"title": "准备开始了吗？", "description": "探索我们全面的组件库，自信地开始构建您的下一个项目。"}, "items": {"responsive": {"title": "响应式设计", "description": "移动优先的方法，无缝的桌面体验"}, "accessible": {"title": "无障碍访问", "description": "符合 WCAG 标准的组件，支持键盘导航"}, "performance": {"title": "性能优化", "description": "针对核心网络指标和快速加载进行优化"}, "seo": {"title": "SEO 就绪", "description": "内置 SEO 优化和元标签管理"}, "i18n": {"title": "国际化", "description": "多语言支持，便于内容管理"}, "darkMode": {"title": "深色模式", "description": "系统感知的主题切换，平滑过渡"}}}}, "footer": {"company": {"name": "Tucsenberg Web", "description": "现代化 B2B 企业网站模板", "address": "企业城商业街123号，EC 12345"}, "products": {"title": "产品", "webDevelopment": "网站开发", "mobileApps": "移动应用", "consulting": "咨询服务", "support": "技术支持"}, "support": {"title": "支持", "documentation": "文档", "helpCenter": "帮助中心", "community": "社区", "contact": "联系我们"}, "contact": {"title": "联系方式", "email": "<EMAIL>", "phone": "+****************", "address": "访问我们"}, "social": {"title": "关注我们", "twitter": "Twitter", "linkedin": "LinkedIn", "github": "GitHub", "youtube": "YouTube"}, "legal": {"copyright": "© 2024 Tucsenberg Web. 保留所有权利。", "privacy": "隐私政策", "terms": "服务条款", "cookies": "<PERSON><PERSON> 政策"}}, "underConstruction": {"title": "建设中", "subtitle": "我们正在努力为您打造精彩内容", "description": "此页面正在开发中。我们正在精心打造值得期待的卓越体验。", "backToHome": "返回首页", "comingSoon": "即将推出"}, "pages": {"products": {"title": "产品服务 - Tucsenberg Web", "description": "探索我们的创新产品和服务，专为帮助您的企业在数字化世界中成长和成功而设计。"}, "blog": {"title": "博客 - Tucsenberg Web", "description": "阅读我们关于网站开发、技术趋势和数字创新的最新见解、教程和行业资讯。"}, "about": {"title": "关于我们 - Tucsenberg Web", "description": "了解我们的公司、使命、团队，以及我们对提供卓越网站解决方案的承诺。"}, "contact": {"title": "联系我们 - Tucsenberg Web", "description": "与我们取得联系，了解我们如何帮助您的企业实现数字化转型和业务增长目标。", "form": {"title": "联系我们", "subtitle": "我们很乐意听到您的声音", "fields": {"name": {"label": "姓名", "placeholder": "请输入您的姓名", "required": "姓名为必填项"}, "email": {"label": "邮箱", "placeholder": "请输入您的邮箱地址", "required": "邮箱为必填项", "invalid": "请输入有效的邮箱地址"}, "purpose": {"label": "沟通事项", "placeholder": "请选择沟通事项", "options": {"purchase": "产品采购", "partnership": "合作代理", "other": "其他咨询"}}, "region": {"label": "所在地区", "placeholder": "请输入您的所在地区（可选）"}, "message": {"label": "附加信息", "placeholder": "请告诉我们更多详情（可选）"}}, "submit": "发送消息", "submitting": "发送中...", "success": "消息发送成功！我们会尽快回复您。", "error": "发送失败，请稍后重试。"}}}, "theme": {"light": "浅色", "dark": "深色", "system": "系统"}}
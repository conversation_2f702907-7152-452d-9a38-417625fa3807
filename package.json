{"name": "tucsenberg-web-nextjs15", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:check": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "format:check": "prettier --check .", "format:fix": "prettier --write .", "format:lint": "pnpm format:fix && pnpm lint:fix", "prepare": "lefthook install", "commitlint": "commitlint --edit"}, "dependencies": {"next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/eslintrc": "^3", "@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "lefthook": "1.11.14", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.8", "tailwindcss": "^4", "typescript": "^5"}}
{"editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.requireConfig": true, "prettier.useEditorConfig": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always"}
{"enhancedVerification": {"enabled": true, "enforceAllLayers": true, "allowEmergencySkip": true, "logAllSteps": true}, "layerConfiguration": {"layer1": {"name": "自动化检查", "script": "scripts/automated-checks.js", "required": true, "timeout": 120, "retries": 2, "parameters": {"mode": "sequential", "includeComplexity": true, "includeBuild": false}}, "layer2": {"name": "AI审查", "script": "scripts/ai-review.js", "required": true, "timeout": 60, "retries": 1, "parameters": {"pragmaticMode": true, "avoidOverEngineering": true, "securityFocus": true}}, "layer3": {"name": "人类确认", "script": "scripts/human-verification.js", "required": true, "timeout": 600, "retries": 3, "parameters": {"autoOpenBrowser": true, "requireAllItems": true, "timeoutMinutes": 10}}}, "scoringCriteria": {"layer1Weight": 0.3, "layer2Weight": 0.4, "layer3Weight": 0.3, "minimumPassingScore": 85, "requireAllLayersPass": true}, "emergencySkip": {"enabled": true, "requiresApproval": true, "approvalMethods": ["environment_variable", "config_file"], "environmentVariable": "VERIFY_TASK_EMERGENCY_SKIP", "logRequired": true, "reasonRequired": true}, "logging": {"directory": ".verification-log", "filePrefix": "enhanced-verify", "includeTimestamps": true, "includeLayerDetails": true, "retentionDays": 30}, "compatibility": {"backwardCompatible": true, "fallbackToOriginal": false, "preserveOriginalAPI": true}, "notifications": {"onStart": true, "onLayerComplete": true, "onFailure": true, "onSuccess": true, "includeTimings": true}, "performance": {"parallelExecution": false, "cacheResults": true, "cacheDuration": 300, "maxConcurrentTasks": 1}}
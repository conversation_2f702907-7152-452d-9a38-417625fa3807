/**
 * Commitlint Configuration
 * 企业级提交信息规范配置
 * 
 * 基于 Conventional Commits 规范
 * https://www.conventionalcommits.org/
 */

module.exports = {
  // 继承 conventional 配置
  extends: ['@commitlint/config-conventional'],
  
  // 自定义规则（企业级严格标准）
  rules: {
    // 类型必须是以下之一
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复bug
        'docs',     // 文档更新
        'style',    // 代码格式化（不影响代码运行的变动）
        'refactor', // 重构（既不是新增功能，也不是修改bug的代码变动）
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'ci',       // CI/CD 相关
        'build',    // 构建系统或外部依赖的变动
        'revert',   // 回滚提交
      ],
    ],
    
    // 类型必须小写
    'type-case': [2, 'always', 'lower-case'],
    
    // 类型不能为空
    'type-empty': [2, 'never'],
    
    // 主题不能为空
    'subject-empty': [2, 'never'],
    
    // 主题不能以句号结尾
    'subject-full-stop': [2, 'never', '.'],
    
    // 主题格式（首字母小写）
    'subject-case': [2, 'always', 'lower-case'],
    
    // 主题最大长度（企业标准：72字符）
    'subject-max-length': [2, 'always', 72],
    
    // 主题最小长度（确保描述足够清晰）
    'subject-min-length': [2, 'always', 10],
    
    // 头部最大长度（包含type、scope、subject）
    'header-max-length': [2, 'always', 100],
    
    // 正文每行最大长度
    'body-max-line-length': [2, 'always', 100],
    
    // 页脚每行最大长度
    'footer-max-line-length': [2, 'always', 100],
    
    // 作用域格式（小写，可选）
    'scope-case': [2, 'always', 'lower-case'],
    
    // 允许的作用域（根据项目结构定义）
    'scope-enum': [
      1, // 警告级别（不强制）
      'always',
      [
        'ui',        // UI组件相关
        'auth',      // 认证相关
        'api',       // API相关
        'config',    // 配置相关
        'deps',      // 依赖相关
        'i18n',      // 国际化相关
        'theme',     // 主题相关
        'perf',      // 性能相关
        'seo',       // SEO相关
        'a11y',      // 无障碍相关
        'security',  // 安全相关
        'test',      // 测试相关
        'docs',      // 文档相关
        'build',     // 构建相关
        'ci',        // CI/CD相关
        'release',   // 发布相关
      ],
    ],
  },
  
  // 忽略的提交类型（用于特殊情况）
  ignores: [
    // 忽略 merge 提交
    (commit) => commit.includes('Merge'),
    // 忽略 revert 提交的详细检查
    (commit) => commit.includes('Revert'),
    // 忽略依赖更新的自动提交
    (commit) => commit.includes('chore(deps)'),
  ],
  
  // 默认忽略规则
  defaultIgnores: true,
  
  // 帮助信息
  helpUrl: 'https://github.com/conventional-changelog/commitlint/#what-is-commitlint',
  
  // 提示信息配置
  prompt: {
    messages: {
      skip: '跳过',
      max: '最多 %d 个字符',
      min: '至少 %d 个字符',
      emptyWarning: '不能为空',
      upperLimitWarning: '超过字符限制',
      lowerLimitWarning: '少于最小字符要求',
    },
    questions: {
      type: {
        description: '选择提交类型:',
        enum: {
          feat: {
            description: '新功能',
            title: 'Features',
            emoji: '✨',
          },
          fix: {
            description: '修复bug',
            title: 'Bug Fixes',
            emoji: '🐛',
          },
          docs: {
            description: '文档更新',
            title: 'Documentation',
            emoji: '📚',
          },
          style: {
            description: '代码格式化',
            title: 'Styles',
            emoji: '💎',
          },
          refactor: {
            description: '代码重构',
            title: 'Code Refactoring',
            emoji: '📦',
          },
          perf: {
            description: '性能优化',
            title: 'Performance Improvements',
            emoji: '🚀',
          },
          test: {
            description: '测试相关',
            title: 'Tests',
            emoji: '🚨',
          },
          chore: {
            description: '构建工具或辅助工具的变动',
            title: 'Chores',
            emoji: '♻️',
          },
        },
      },
      scope: {
        description: '影响范围 (可选):',
      },
      subject: {
        description: '简短描述:',
      },
      body: {
        description: '详细描述 (可选):',
      },
      isBreaking: {
        description: '是否包含破坏性变更?',
      },
      breakingBody: {
        description: '破坏性变更的详细描述:',
      },
      breaking: {
        description: '破坏性变更说明:',
      },
      isIssueAffected: {
        description: '是否影响未关闭的issue?',
      },
      issuesBody: {
        description: '如果有issue被关闭，请描述:',
      },
      issues: {
        description: '关联的issue (例如: "fix #123", "re #123"):',
      },
    },
  },
};

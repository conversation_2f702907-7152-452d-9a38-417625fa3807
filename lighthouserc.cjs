/**
 * Lighthouse CI 配置文件
 * 企业级性能监控配置 - 确保网站达到高性能标准
 */

module.exports = {
  ci: {
    // 收集配置
    collect: {
      // 要测试的URL列表
      url: ['http://localhost:3000'],
      // 每个URL运行的次数
      numberOfRuns: 3,
      // 启动服务器配置
      startServerCommand: 'pnpm build && pnpm start',
      startServerReadyPattern: 'Ready on',
      startServerReadyTimeout: 30000,
      // Chrome启动选项
      chromePath: undefined, // 使用系统默认Chrome
      chromeFlags: [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--headless',
      ],
      // 设备模拟
      settings: {
        // 使用移动设备配置进行测试
        emulatedFormFactor: 'mobile',
        // 网络节流配置
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0,
        },
        // 屏幕模拟
        screenEmulation: {
          mobile: true,
          width: 375,
          height: 667,
          deviceScaleFactor: 2,
          disabled: false,
        },
      },
    },

    // 断言配置 - 企业级性能标准
    assert: {
      // 性能预算配置
      assertions: {
        // Core Web Vitals 指标
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'first-contentful-paint': ['error', { maxNumericValue: 1800 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        'speed-index': ['error', { maxNumericValue: 3000 }],

        // Lighthouse 分数要求 (企业级标准: ≥90)
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        'categories:pwa': ['warn', { minScore: 0.8 }],

        // 资源优化检查
        'unused-css-rules': ['warn', { maxLength: 0 }],
        'unused-javascript': ['warn', { maxLength: 0 }],
        'modern-image-formats': ['error', { maxLength: 0 }],
        'uses-optimized-images': ['error', { maxLength: 0 }],
        'uses-webp-images': ['warn', { maxLength: 0 }],
        'uses-responsive-images': ['error', { maxLength: 0 }],

        // 网络优化
        'uses-http2': ['error', { maxLength: 0 }],
        'uses-long-cache-ttl': ['warn', { maxLength: 0 }],
        'efficient-animated-content': ['warn', { maxLength: 0 }],

        // JavaScript 优化
        'unminified-javascript': ['error', { maxLength: 0 }],
        'unminified-css': ['error', { maxLength: 0 }],
        'render-blocking-resources': ['warn', { maxLength: 0 }],

        // 可访问性检查
        'color-contrast': ['error', { maxLength: 0 }],
        'image-alt': ['error', { maxLength: 0 }],
        label: ['error', { maxLength: 0 }],
        'link-name': ['error', { maxLength: 0 }],

        // SEO 优化
        'meta-description': ['error', { maxLength: 0 }],
        'document-title': ['error', { maxLength: 0 }],
        hreflang: ['warn', { maxLength: 0 }],
        canonical: ['warn', { maxLength: 0 }],
      },

      // 预设配置
      preset: 'lighthouse:recommended',

      // 包含所有失败的断言
      includePassedAssertions: false,
    },

    // 上传配置 (可选 - 用于CI/CD集成)
    upload: {
      // 临时文件服务器 (开发环境)
      target: 'temporary-public-storage',
      // 生产环境可配置为:
      // target: 'lhci',
      // serverBaseUrl: 'https://your-lhci-server.com',
      // token: process.env.LHCI_TOKEN,
    },

    // 服务器配置 (如果使用LHCI服务器)
    server: {
      // port: 9001,
      // storage: {
      //   storageMethod: 'sql',
      //   sqlDialect: 'sqlite',
      //   sqlDatabasePath: './lhci.db',
      // },
    },

    // Wizard配置 (首次设置)
    wizard: {
      // 跳过向导，使用预定义配置
      skip: true,
    },
  },

  // 自定义配置
  extends: [
    // 可以扩展其他配置文件
    // '@lhci/utils/src/presets/all.js'
  ],

  // 环境变量配置
  env: {
    // CI环境检测
    CI: process.env.CI || false,
    // 构建目录
    BUILD_DIR: process.env.BUILD_DIR || '.next',
    // 端口配置
    PORT: process.env.PORT || 3000,
  },

  // 钩子函数
  hooks: {
    // 收集前钩子
    beforeCollect: async () => {
      console.log('🚀 开始 Lighthouse 性能测试...');
    },

    // 收集后钩子
    afterCollect: async (results) => {
      console.log(`📊 性能测试完成，共测试 ${results.length} 个页面`);

      // 计算平均分数
      const avgScores = results.reduce(
        (acc, result) => {
          const categories = result.lhr.categories;
          acc.performance += categories.performance.score || 0;
          acc.accessibility += categories.accessibility.score || 0;
          acc.bestPractices += categories['best-practices'].score || 0;
          acc.seo += categories.seo.score || 0;
          acc.pwa += categories.pwa.score || 0;
          return acc;
        },
        { performance: 0, accessibility: 0, bestPractices: 0, seo: 0, pwa: 0 }
      );

      const count = results.length;
      console.log('📈 平均分数:');
      console.log(
        `  性能: ${Math.round((avgScores.performance / count) * 100)}/100`
      );
      console.log(
        `  可访问性: ${Math.round((avgScores.accessibility / count) * 100)}/100`
      );
      console.log(
        `  最佳实践: ${Math.round((avgScores.bestPractices / count) * 100)}/100`
      );
      console.log(`  SEO: ${Math.round((avgScores.seo / count) * 100)}/100`);
      console.log(`  PWA: ${Math.round((avgScores.pwa / count) * 100)}/100`);
    },

    // 断言前钩子
    beforeAssert: async () => {
      console.log('🔍 开始性能断言检查...');
    },

    // 断言后钩子
    afterAssert: async (assertionResults) => {
      const passed = assertionResults.filter((r) => r.level !== 'error').length;
      const failed = assertionResults.filter((r) => r.level === 'error').length;

      console.log(`✅ 断言检查完成: ${passed} 通过, ${failed} 失败`);

      if (failed > 0) {
        console.log('❌ 性能测试未达到企业级标准，请优化后重试');
        console.log('💡 优化建议:');
        console.log('  1. 优化图片格式和大小');
        console.log('  2. 减少JavaScript包大小');
        console.log('  3. 启用HTTP/2和缓存');
        console.log('  4. 优化CSS和字体加载');
      } else {
        console.log('🎉 恭喜！网站性能达到企业级标准');
      }
    },
  },
};

{"humanVerification": {"enabled": true, "timeoutMinutes": 10, "maxRetries": 3, "requireAllItems": true, "emergencySkip": {"enabled": true, "requiresApproval": true, "logRequired": true}}, "verificationPrinciples": {"operability": "使用具体命令或操作步骤", "visibility": "检查文件存在或界面元素", "objectivity": "避免主观技术判断", "timeliness": "控制在3-10分钟内完成"}, "checklistTemplates": {"basic": {"name": "基础任务确认清单", "estimatedTime": "3-5分钟", "items": [{"id": "file_existence", "type": "file_check", "description": "能看到 {filePath} 文件已创建", "instruction": "在文件管理器或编辑器中确认文件存在", "required": true}, {"id": "command_execution", "type": "command_check", "description": "运行 {command} 显示 {expectedResult}", "instruction": "在终端中执行命令并确认输出结果", "required": true}, {"id": "browser_verification", "type": "browser_check", "description": "访问 {url} 能正常显示 {content}", "instruction": "在浏览器中访问URL并确认页面内容", "required": false}]}, "feature": {"name": "功能任务确认清单", "estimatedTime": "5-10分钟", "items": [{"id": "functionality_test", "type": "interaction_check", "description": "点击 {element} 有 {expectedReaction}", "instruction": "在界面中进行交互操作并确认反应", "required": true}, {"id": "visual_verification", "type": "visual_check", "description": "界面显示 {expectedUI} 元素", "instruction": "确认界面元素正确显示和布局", "required": true}, {"id": "responsive_check", "type": "responsive_check", "description": "在不同屏幕尺寸下功能正常", "instruction": "调整浏览器窗口大小测试响应式效果", "required": false}]}, "integration": {"name": "集成任务确认清单", "estimatedTime": "7-10分钟", "items": [{"id": "service_integration", "type": "service_check", "description": "服务 {serviceName} 正常响应", "instruction": "测试服务接口或功能集成", "required": true}, {"id": "data_flow", "type": "data_check", "description": "数据流 {dataFlow} 正确传递", "instruction": "验证数据在系统间正确传递", "required": true}, {"id": "error_handling", "type": "error_check", "description": "错误情况 {errorCase} 正确处理", "instruction": "测试错误场景的处理机制", "required": false}]}}, "verificationFlow": {"steps": ["generateChecklist", "displayInstructions", "waitForUserConfirmation", "validateResponses", "generateVerificationToken", "logResults"]}, "tokenGeneration": {"algorithm": "timestamp-based", "includeTaskId": true, "includeUserAgent": false, "expirationHours": 24}, "logging": {"directory": ".verification-log", "fileFormat": "verification-{taskId}-{timestamp}.json", "includeScreenshots": false, "retentionDays": 30}, "browserIntegration": {"autoOpen": true, "defaultUrl": "http://localhost:3000", "reminderInterval": 30, "maxReminders": 5}}
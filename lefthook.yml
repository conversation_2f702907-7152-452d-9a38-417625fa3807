# Lefthook Git Hooks Configuration
# 企业级代码质量保障体系

# Pre-commit hooks - 提交前代码质量检查
pre-commit:
  parallel: true
  commands:
    # TypeScript 类型检查（最高优先级）
    type-check:
      glob: '*.{ts,tsx}'
      run: pnpm type-check
      stage_fixed: true
      fail_text: 'TypeScript 类型检查失败，请修复类型错误后重新提交'

    # ESLint 代码规范检查
    lint-check:
      glob: '*.{js,jsx,ts,tsx}'
      run: pnpm lint:check
      stage_fixed: true
      fail_text: "ESLint 检查失败，请运行 'pnpm lint:fix' 修复代码规范问题"

    # Prettier 代码格式化检查
    format-check:
      glob: '*.{js,jsx,ts,tsx,json,css,md}'
      run: pnpm format:check
      stage_fixed: true
      fail_text: "代码格式不符合规范，请运行 'pnpm format:fix' 格式化代码"

    # 防止大文件提交（企业安全要求）
    file-size-check:
      glob: '*'
      run: |
        find . -type f -size +5M -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./.git/*" | while read file; do
          echo "错误: 文件 $file 超过 5MB 限制，请优化文件大小或使用 Git LFS"
          exit 1
        done
      fail_text: '检测到大文件，请优化文件大小或使用 Git LFS 管理大文件'

    # 敏感信息检查
    secrets-check:
      glob: '*.{js,jsx,ts,tsx,json,env*,md}'
      run: |
        if grep -r -E "(password|secret|key|token|api_key)" --include="*.js" --include="*.jsx" --include="*.ts" --include="*.tsx" --include="*.json" --exclude-dir=node_modules --exclude-dir=.next --exclude-dir=.git . | grep -v -E "(// |/\*|\* |#|password.*placeholder|secretKey.*example)"; then
          echo "错误: 检测到可能的敏感信息，请检查并移除"
          exit 1
        fi
      fail_text: '检测到可能的敏感信息，请检查代码中是否包含密码、密钥等敏感数据'

# Commit message hooks - 提交信息规范检查
commit-msg:
  commands:
    commitlint:
      run: pnpm commitlint --edit {1}
      fail_text: |
        提交信息格式不符合规范！

        正确格式：<type>(<scope>): <subject>

        类型 (type):
        - feat: 新功能
        - fix: 修复bug
        - docs: 文档更新
        - style: 代码格式化
        - refactor: 代码重构
        - test: 测试相关
        - chore: 构建工具或辅助工具的变动

        示例:
        - feat(auth): 添加用户登录功能
        - fix(ui): 修复按钮样式问题
        - docs: 更新README文档

# Pre-push hooks - 推送前最终检查
pre-push:
  commands:
    # 构建测试（确保代码能正常构建）
    build-test:
      run: pnpm build
      fail_text: '构建失败，请修复构建错误后重新推送'

    # 最终代码质量检查
    final-quality-check:
      run: |
        echo "🔍 执行最终代码质量检查..."
        pnpm type-check && pnpm lint:check && pnpm format:check
        echo "✅ 代码质量检查通过"
      fail_text: '最终代码质量检查失败，请确保所有检查都通过'

# 跳过钩子的配置（紧急情况使用）
skip_output:
  - meta
  - summary

# 并行执行配置
parallel: true

# 最小版本要求
min_version: 1.11.0

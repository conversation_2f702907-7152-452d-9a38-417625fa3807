module.exports = {
  ci: {
    collect: {
      url: ['http://localhost:3000/en', 'http://localhost:3000/zh'],
      startServerCommand: 'pnpm build && pnpm start',
      startServerReadyPattern: /Ready on|ready|listening/i,
      startServerReadyTimeout: 60000,
      numberOfRuns: 3,
      settings: {
        // 优化 Lighthouse 设置
        chromeFlags: '--no-sandbox --disable-dev-shm-usage',
        preset: 'desktop',
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0,
        },
      },
    },
    assert: {
      assertions: {
        // 调整性能阈值为更实际的值
        'categories:performance': ['warn', { minScore: 0.7 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.8 }],
        'categories:seo': ['warn', { minScore: 0.8 }],
        'categories:pwa': 'off',

        // 具体指标优化
        'first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['warn', { maxNumericValue: 4000 }],
        'cumulative-layout-shift': ['warn', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['warn', { maxNumericValue: 300 }],

        // 关闭一些不适用的检查
        redirects: 'off',
        'legacy-javascript': 'off',
        'uses-webp-images': 'off',
        'document-latency-insight': 'off',
        'legacy-javascript-insight': 'off',
        'render-blocking-insight': 'off',
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
};

# AI Agent 開發守則

## 項目概述

**項目類型**: Next.js 15 + React 19 + TypeScript 5.8 + Tailwind CSS 4 企業級 B2B 網站
**架構模式**: App Router + src/ 目錄 + 國際化 + 主題系統
**質量標準**: 三層質量保障體系（自動化檢查 → AI技術審查 → 人類確認）

## 強制架構約束

### 目錄結構規範
- **必須使用**: `src/` 目錄結構，禁止在根目錄創建組件
- **組件分類**:
  - `src/components/ui/` - shadcn/ui 組件
  - `src/components/layout/` - 佈局組件（導航、頁腳）
  - `src/components/content/` - 內容組件（卡片、列表）
  - `src/components/shared/` - 共享組件（按鈕、表單）
- **類型定義**: 必須放在 `src/types/`
- **工具函數**: 必須放在 `src/lib/`
- **頁面路由**: 必須使用 `src/app/` App Router 結構

### App Router 強制規則
- **禁止**: 使用 pages/ 目錄
- **必須**: 所有路由使用 `src/app/` 結構
- **必須**: 使用 layout.tsx 和 page.tsx 命名
- **必須**: 支持 `[locale]` 動態路由用於國際化

## 文件連動規範

### 多語言同步要求
**觸發條件**: 修改任何語言相關文件時
**必須同步**:
- `messages/en.json` ↔ `messages/zh.json`
- `content/en/` ↔ `content/zh/`
- `src/app/[locale]/` 下的所有頁面文件

### 文檔同步要求
**觸發條件**: 修改文檔文件時
**必須同步**:
- `README.md` → 必須同步更新 `docs/zh/README.md`
- `docs/technology/` → 必須同步更新對應的中文版本

### 配置文件連動
**觸發條件**: 修改配置文件時
**必須檢查**:
- 修改 `package.json` → 檢查 `pnpm-lock.yaml` 是否需要更新
- 修改 `tailwind.config.ts` → 檢查 `src/lib/utils.ts` 中的 cn() 函數
- 修改 `next.config.ts` → 檢查 `src/middleware.ts` 國際化配置

## 組件開發規範

### shadcn/ui 強制要求
- **必須使用**: New York 風格變體
- **必須支持**: dark/light 主題切換
- **禁止**: 修改 shadcn/ui 組件的核心樣式
- **必須**: 使用 `cn()` 函數合併 className

### 組件創建規則
```typescript
// ✅ 正確示例
export function CustomButton({ className, ...props }: ButtonProps) {
  return (
    <Button 
      className={cn("custom-styles", className)} 
      {...props} 
    />
  )
}

// ❌ 錯誤示例
export function CustomButton({ style, ...props }: ButtonProps) {
  return <Button style={{...customStyles, ...style}} {...props} />
}
```

### 主題系統要求
- **必須**: 所有組件支持 `dark:` 前綴類名
- **必須**: 使用 CSS 變量定義顏色
- **禁止**: 硬編碼顏色值

## 動畫系統決策樹

### 動畫實現優先級
1. **第一優先**: Tailwind CSS 動畫類名
   - `animate-fade-in`, `animate-slide-in-from-top` 等
   - `transition-*`, `duration-*`, `ease-*` 類名
2. **第二優先**: Intersection Observer + Tailwind CSS
   - 用於滾動觸發動畫
3. **最後選擇**: framer-motion
   - 僅在前兩者無法滿足需求時使用

### 動畫決策流程
```
需要動畫效果？
├─ 是 → Tailwind CSS 能實現？
│   ├─ 是 → 使用 Tailwind CSS 動畫
│   └─ 否 → 需要滾動觸發？
│       ├─ 是 → Intersection Observer + Tailwind CSS
│       └─ 否 → 評估是否真的需要 framer-motion
└─ 否 → 不添加動畫
```

### 動畫禁止事項
- **禁止**: 直接使用 CSS 動畫（@keyframes）
- **禁止**: 內聯 style 動畫
- **禁止**: 同時使用多個動畫庫

## 質量保障流程

### 三層檢查體系
**第一層 - 自動化基礎檢查**:
- `pnpm type-check` - TypeScript 類型檢查
- `pnpm lint:check` - ESLint 代碼規範
- `pnpm format:check` - Prettier 格式檢查
- `next build` - Next.js 構建驗證

**第二層 - AI技術審查**:
- 代碼質量評估（避免過度複雜）
- 架構一致性驗證
- 性能影響評估
- 安全性檢查

**第三層 - 人類簡化確認**:
- 功能正常工作
- 視覺效果符合預期
- 響應式設計正確
- 無明顯性能問題

### 質量標準
- **自動化檢查**: 100% 通過率
- **AI技術審查**: ≥90分
- **人類確認**: ≤5分鐘完成

## AI 決策規範

### 優先級判斷標準
1. **安全性** > 功能性 > 性能 > 美觀性
2. **用戶體驗** > 開發體驗 > 代碼簡潔性
3. **標準化** > 自定義實現
4. **漸進式增強** > 一次性完美實現

### 模糊情況處理
**當遇到不確定情況時**:
1. 檢查 `.augment/rules/` 中的相關規範
2. 參考現有代碼的實現模式
3. 選擇最保守和標準的方案
4. 優先考慮可維護性

### 技術選擇決策樹
```
需要新功能？
├─ 檢查 shadcn/ui 是否有現成組件
├─ 檢查 Tailwind CSS 是否能實現
├─ 檢查現有 src/lib/ 是否有工具函數
└─ 最後考慮添加新依賴
```

## 嚴格禁止事項

### 架構禁止
- **禁止**: 在根目錄創建組件文件
- **禁止**: 使用 pages/ 目錄（必須使用 App Router）
- **禁止**: 混合使用 App Router 和 Pages Router
- **禁止**: 在 src/ 外創建業務邏輯文件

### 代碼禁止
- **禁止**: 使用 `any` 類型（除非極特殊情況）
- **禁止**: 內聯樣式（style 屬性）
- **禁止**: 硬編碼字符串（必須使用國際化）
- **禁止**: 直接修改 node_modules 文件

### 依賴禁止
- **禁止**: 安裝未經批准的 UI 庫（已有 shadcn/ui）
- **禁止**: 安裝重複功能的包
- **禁止**: 使用過時或不維護的包

### 文件操作禁止
- **禁止**: 刪除 `.augment/rules/` 目錄下的文件
- **禁止**: 修改 `package-lock.json` 或 `pnpm-lock.yaml`
- **禁止**: 直接編輯自動生成的文件

## 緊急情況處理

### 構建失敗時
1. 檢查 TypeScript 錯誤
2. 檢查 ESLint 錯誤
3. 檢查依賴版本衝突
4. 檢查導入路徑錯誤

### 性能問題時
1. 檢查是否有不必要的重新渲染
2. 檢查是否有大型依賴包
3. 檢查圖片和資源優化
4. 檢查動畫性能影響

### 國際化問題時
1. 檢查 messages/ 文件同步
2. 檢查 locale 路由配置
3. 檢查 middleware.ts 設置
4. 檢查 next-intl 配置

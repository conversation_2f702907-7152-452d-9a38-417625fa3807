/** @type {import('prettier').Config} */
const config = {
  // 基础 Prettier 配置
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'all',
  printWidth: 80,
  endOfLine: 'lf',

  // Import 排序插件配置
  plugins: ['@trivago/prettier-plugin-sort-imports'],

  // Import 排序规则
  importOrder: [
    // 1. React 相关 (最高优先级)
    '^react$',
    '^react/(.*)$',

    // 2. Next.js 相关
    '^next',
    '^next/(.*)$',

    // 3. 第三方库 (node_modules)
    '^[a-z]',
    '^@[a-z]',

    // 4. 内部模块 (@/ 别名)
    '^@/(.*)$',

    // 5. 相对路径导入
    '^[./]',
  ],

  // Import 排序选项
  importOrderSeparation: true, // 在不同组之间添加空行
  importOrderSortSpecifiers: true, // 对同一导入语句中的说明符排序
  importOrderBuiltinModulesToTop: true, // 内置模块排在最前面
  importOrderParserPlugins: ['typescript', 'jsx', 'decorators-legacy'],
  importOrderMergeDuplicateImports: true, // 合并重复导入
  importOrderCombineTypeAndValueImports: false, // 保持类型导入分离
};

export default config;

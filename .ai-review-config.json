{"pragmaticMode": true, "avoidOverEngineering": true, "reviewSettings": {"codeReview": {"enabled": true, "model": "gemini-2.5-pro", "thinking_mode": "medium", "focus": "实用性和可维护性", "avoidPatterns": ["过度抽象", "不必要的设计模式", "过早优化", "过度复杂化"]}, "securityAudit": {"enabled": true, "model": "gemini-2.5-pro", "thinking_mode": "high", "focus": "实际安全风险", "priorityLevels": ["critical", "high", "medium"]}, "architectureAnalysis": {"enabled": true, "model": "gemini-2.5-flash", "thinking_mode": "medium", "focus": "架构一致性和简洁性"}}, "scoringCriteria": {"basicTasks": {"minimumScore": 85, "weightings": {"functionality": 40, "maintainability": 30, "security": 20, "performance": 10}}, "featureTasks": {"minimumScore": 90, "weightings": {"functionality": 35, "maintainability": 25, "security": 20, "performance": 10, "userExperience": 10}}}, "reportTemplate": {"sections": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "functionalityAssessment", "securityAnalysis", "maintainabilityReview", "performanceConsiderations", "pragmaticRecommendations", "actionableItems"], "maxRecommendations": 5, "focusOnActionable": true}, "pragmaticGuidelines": {"prioritizeReadability": true, "avoidPrematureOptimization": true, "maintainSimplicity": true, "focusOnBusinessValue": true, "balanceQualityAndDelivery": true}, "emergencySkip": {"enabled": true, "requiresApproval": true, "logRequired": true}}
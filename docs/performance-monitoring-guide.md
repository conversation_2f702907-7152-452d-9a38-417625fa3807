# 性能监控指南 (Lighthouse CI + Web Vitals)

## 概述

本项目配置了企业级性能监控系统，包括Lighthouse CI和Web Vitals监控，确保网站达到高性能标准。性能监控分为三个层次：

1. **自动化基础检查** - Lighthouse CI、Web Vitals收集、包大小分析
2. **AI技术审查** - 性能配置合理性评估、优化建议分析
3. **人类简化确认** - 性能报告查看、指标验证

## 性能监控工具

### 1. Lighthouse CI

**企业级性能标准：**
- **性能分数**: ≥ 90/100
- **可访问性**: ≥ 95/100
- **最佳实践**: ≥ 90/100
- **SEO**: ≥ 90/100
- **PWA**: ≥ 80/100

**配置文件**: `lighthouserc.js`

### 2. Web Vitals 监控

**Core Web Vitals 阈值：**
- **LCP (Largest Contentful Paint)**: ≤ 2.5秒
- **FID (First Input Delay)**: ≤ 100毫秒
- **CLS (Cumulative Layout Shift)**: ≤ 0.1
- **FCP (First Contentful Paint)**: ≤ 1.8秒
- **TTFB (Time to First Byte)**: ≤ 800毫秒

**实现文件**: `src/lib/web-vitals.ts`

### 3. 包大小监控

**大小限制：**
- **初始包大小**: ≤ 200KB
- **总包大小**: ≤ 1MB

## 使用方法

### 基础命令

```bash
# Lighthouse CI 性能测试
pnpm lighthouse:ci

# 性能分析（3次运行）
pnpm perf:analyze

# 完整性能监控
pnpm perf:monitor

# 性能检查（推荐）
pnpm perf:check

# 全面性能测试
pnpm perf:full
```

### 集成到开发流程

```bash
# 开发前性能检查
pnpm quality:check && pnpm perf:check

# 构建后性能验证
pnpm build && pnpm perf:check

# 完整质量+性能检查
pnpm quality:check && pnpm security:check && pnpm perf:check
```

## Web Vitals 集成

### 1. 在应用中集成性能监控

```tsx
// src/app/layout.tsx
import { WebVitalsProvider } from '@/components/performance/web-vitals-provider';

export default function RootLayout({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  return (
    <html lang="zh-CN">
      <body>
        <WebVitalsProvider 
          debug={process.env.NODE_ENV === 'development'}
          onMetric={(metric) => {
            // 自定义性能指标处理
            console.log('Performance metric:', metric);
          }}
        >
          {children}
        </WebVitalsProvider>
      </body>
    </html>
  );
}
```

### 2. 使用性能指标钩子

```tsx
// 在组件中使用性能指标
import { useWebVitals } from '@/components/performance/web-vitals-provider';

export function PerformanceStatus() {
  const { metrics, getSummary } = useWebVitals();
  const summary = getSummary();

  return (
    <div>
      <h3>性能状态: {summary?.rating}</h3>
      <p>总体评分: {summary?.score}/100</p>
      {metrics.map(metric => (
        <div key={metric.name}>
          {metric.name}: {metric.value}ms ({metric.rating})
        </div>
      ))}
    </div>
  );
}
```

### 3. 性能调试面板

在开发环境中启用调试面板：

```tsx
<WebVitalsProvider debug={true}>
  {children}
</WebVitalsProvider>
```

## 性能报告

### Lighthouse 报告

运行 `pnpm lighthouse:ci` 后，报告将保存在：
- 临时在线存储（开发环境）
- 本地 `.lighthouseci/` 目录

### 性能监控报告

运行 `pnpm perf:monitor` 后，报告将保存在：
- `performance-reports/performance-report-[timestamp].json`
- `performance-reports/performance-report-[timestamp].html`

**报告内容：**
```json
{
  "timestamp": "2025-01-24T...",
  "lighthouse": {
    "categories": {
      "performance": { "score": 0.92 },
      "accessibility": { "score": 0.96 },
      "best-practices": { "score": 0.91 },
      "seo": { "score": 0.93 },
      "pwa": { "score": 0.85 }
    }
  },
  "bundleAnalysis": {
    "initialBundle": 150000,
    "totalBundle": 800000
  },
  "summary": {
    "overallScore": 92,
    "status": "excellent",
    "passed": 8,
    "failed": 0,
    "warnings": 1
  },
  "recommendations": []
}
```

## 性能优化建议

### 1. Core Web Vitals 优化

#### LCP (Largest Contentful Paint) 优化
```tsx
// 使用 Next.js Image 组件
import Image from 'next/image';

<Image
  src="/hero-image.jpg"
  alt="Hero"
  width={800}
  height={600}
  priority // 关键图片优先加载
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

#### CLS (Cumulative Layout Shift) 优化
```css
/* 为图片和视频设置固定尺寸 */
.image-container {
  width: 100%;
  height: 400px;
  position: relative;
}

/* 避免动态插入内容 */
.dynamic-content {
  min-height: 200px; /* 预留空间 */
}
```

#### FID (First Input Delay) 优化
```tsx
// 使用 React.lazy 进行代码分割
const HeavyComponent = React.lazy(() => import('./HeavyComponent'));

// 延迟加载非关键JavaScript
useEffect(() => {
  const timer = setTimeout(() => {
    import('./non-critical-module');
  }, 1000);
  return () => clearTimeout(timer);
}, []);
```

### 2. 包大小优化

```javascript
// next.config.ts
const nextConfig = {
  // 启用包分析
  experimental: {
    bundlePagesRouterDependencies: true,
  },
  
  // 优化图片
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 31536000,
  },
  
  // 压缩配置
  compress: true,
  
  // 移除未使用的代码
  swcMinify: true,
};
```

### 3. 缓存策略优化

```tsx
// 静态生成 + ISR
export async function generateStaticParams() {
  return [
    { slug: 'page1' },
    { slug: 'page2' },
  ];
}

export const revalidate = 3600; // 1小时重新验证
```

## CI/CD 集成

### GitHub Actions 示例

```yaml
# .github/workflows/performance.yml
name: Performance Monitoring
on: [push, pull_request]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Build application
        run: pnpm build
      
      - name: Run performance tests
        run: pnpm perf:full
      
      - name: Upload performance reports
        uses: actions/upload-artifact@v4
        with:
          name: performance-reports
          path: performance-reports/
```

### Vercel 部署钩子

```javascript
// vercel.json
{
  "buildCommand": "pnpm build && pnpm perf:check",
  "functions": {
    "src/app/api/web-vitals/route.ts": {
      "maxDuration": 10
    }
  }
}
```

## 性能监控 API

### Web Vitals 数据收集 API

```tsx
// src/app/api/web-vitals/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const data = await request.json();
  
  // 存储性能数据到数据库或分析服务
  console.log('Web Vitals data:', data);
  
  // 可以集成到:
  // - Google Analytics
  // - Vercel Analytics
  // - 自定义分析服务
  
  return NextResponse.json({ success: true });
}
```

## 故障排除

### 常见问题

1. **Lighthouse CI 超时**
   ```bash
   # 增加超时时间
   LHCI_COLLECT_TIMEOUT=60000 pnpm lighthouse:ci
   ```

2. **性能分数波动**
   ```bash
   # 增加测试次数获得更稳定的结果
   pnpm perf:analyze # 默认3次运行
   ```

3. **Web Vitals 数据缺失**
   ```tsx
   // 确保在客户端环境中初始化
   useEffect(() => {
     if (typeof window !== 'undefined') {
       initWebVitals();
     }
   }, []);
   ```

### 性能调试

```bash
# 启用详细日志
DEBUG=lhci:* pnpm lighthouse:ci

# 分析包大小
pnpm build && npx @next/bundle-analyzer

# 检查运行时性能
pnpm dev # 在浏览器开发者工具中查看Performance面板
```

## 最佳实践

### 1. 定期性能检查
- 每次构建后运行性能测试
- 定期审查性能报告
- 监控性能趋势变化

### 2. 性能预算管理
- 设置严格的性能阈值
- 在CI/CD中强制执行性能标准
- 定期评估和调整性能目标

### 3. 持续优化
- 基于真实用户数据优化
- 关注关键用户路径的性能
- 平衡功能需求和性能要求

### 4. 团队协作
- 将性能指标纳入代码审查
- 培训团队成员性能优化技能
- 建立性能问题的快速响应机制

## 获取帮助

- Lighthouse CI 文档：https://github.com/GoogleChrome/lighthouse-ci
- Web Vitals 文档：https://web.dev/vitals/
- Next.js 性能优化：https://nextjs.org/docs/advanced-features/measuring-performance
- 项目性能问题请联系开发团队

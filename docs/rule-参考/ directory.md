Please adapt the globs depending on your project structure.

---
name: nextjs-best-practices.mdc
description: Best practices for Next.js applications and routing
globs: **/*.{ts,tsx}
---

- Use the App Router for better performance and organization.
- Implement proper error boundaries to handle errors gracefully.
- Optimize data fetching with `getServerSideProps` or `getStaticProps` as needed.
- Utilize dynamic routing for cleaner URL structures.

---
name: react-best-practices.mdc
description: Best practices for React development
globs: **/*.{ts,tsx,js,jsx}
---

- Use functional components and hooks for state management.
- Keep components small and focused on a single responsibility.
- Leverage React's Context API for global state management.
- Optimize performance with `React.memo` and `useMemo`.

---
name: typescript-best-practices.mdc 
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}
---

- Use strict null checks to avoid runtime errors.
- Prefer interfaces over types for object shapes.
- Implement type guards and assertions for better type safety.
- Utilize generics for reusable components and functions.

---
name: tailwindcss-best-practices.mdc
description: Guidelines for styling with Tailwind CSS
globs: **/*.{ts,tsx,css}
---

- Use utility-first classes for rapid styling.
- Create custom themes using Tailwind's configuration.
- Leverage `@apply` for reusable styles in your CSS.
- Ensure responsive design by using Tailwind's responsive utilities.

---
name: eslint-best-practices.mdc
description: Best practices for ESLint configuration and usage
globs: **/*.{ts,tsx,js,jsx}
---

- Use `eslint-config-next` for Next.js specific linting rules.
- Enforce consistent coding styles with Prettier integration.
- Set up rules for accessibility checks using `eslint-plugin-jsx-a11y`.
- Regularly run linting as part of your CI/CD pipeline.

---
name: vitest-best-practices.mdc
description: Best practices for testing with Vitest
globs: **/*.{ts,tsx}
---

- Write unit tests for all components and utility functions.
- Use `describe` and `it` blocks for clear test organization.
- Leverage coverage reports to identify untested code.
- Mock external dependencies to isolate tests effectively.

---
name: commitlint-best-practices.mdc
description: Best practices for commit message linting
globs: **/*.md
---

- Follow conventional commit messages for clarity and consistency.
- Use `@commitlint/config-conventional` for standard rules.
- Integrate commit linting into your pre-commit hooks for enforcement.
- Document commit message guidelines in your project's README.
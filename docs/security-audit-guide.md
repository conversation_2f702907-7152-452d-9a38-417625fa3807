# 安全审计和依赖检查指南

## 概述

本项目配置了企业级安全审计和依赖检查工具，确保代码和依赖的安全性。安全检查分为三个层次：

1. **自动化基础检查** - ESLint安全规则、依赖漏洞扫描
2. **AI技术审查** - 代码安全分析、最佳实践评估  
3. **人类简化确认** - 最终安全验收

## 安全检查工具

### 1. ESLint 安全插件

**已配置的安全规则：**
- `security/detect-object-injection` - 检测对象注入漏洞
- `security/detect-non-literal-fs-filename` - 检测文件系统操作风险
- `security/detect-unsafe-regex` - 检测不安全的正则表达式
- `security/detect-buffer-noassert` - 检测Buffer操作风险
- `security/detect-child-process` - 检测子进程执行风险
- `security/detect-eval-with-expression` - 检测eval使用
- `security/detect-non-literal-require` - 检测动态require
- `security/detect-possible-timing-attacks` - 检测时序攻击风险

### 2. 自定义安全扫描

**检查内容：**
- 敏感信息泄露（密码、密钥、Token等）
- 不安全的函数调用（eval、innerHTML等）
- 动态导入安全风险
- 文件权限检查

**扫描范围：**
- `.js`, `.jsx`, `.ts`, `.tsx`, `.json`, `.md` 文件
- 排除 `node_modules`, `.next`, `.git` 等目录

### 3. 依赖安全审计

**检查项目：**
- 依赖包漏洞扫描
- 过时依赖包检查
- package.json 配置验证
- 不安全依赖源检测

**严重性阈值：**
- 严重漏洞：0个（不允许）
- 高危漏洞：0个（不允许）
- 中危漏洞：最多5个
- 低危漏洞：最多10个

## 使用方法

### 基础命令

```bash
# 运行自定义安全扫描
pnpm security:scan

# 运行依赖安全审计
pnpm security:deps

# 运行pnpm官方审计
pnpm security:audit

# 运行完整安全检查（推荐）
pnpm security:check

# 运行全面安全检查
pnpm security:full
```

### 集成到开发流程

```bash
# 开发前检查
pnpm quality:check && pnpm security:check

# 提交前检查（已集成到lefthook）
git commit -m "feat: 新功能"  # 自动运行安全检查

# 推送前检查
pnpm security:full && git push
```

### CI/CD 集成

```bash
# 在CI流水线中添加
- name: Security Audit
  run: |
    pnpm install
    pnpm security:full
```

## 安全检查报告

### 自定义安全扫描报告

```
📊 安全检查报告
==================================================
扫描文件数: 78
扫描耗时: 3085ms
发现问题: 0
  - 严重: 0
  - 高危: 0
  - 中危: 0
  - 低危: 0
==================================================
✅ 安全检查通过: 未发现安全问题
```

### 依赖审计报告

```
📊 依赖安全审计报告
============================================================
审计耗时: 2826ms
漏洞总数: 0
  - 严重: 0 (阈值: 0)
  - 高危: 0 (阈值: 0)
  - 中危: 0 (阈值: 5)
  - 低危: 0 (阈值: 10)
过时包数: 0
============================================================
✅ 依赖安全审计通过: 所有依赖都是安全的
```

## 安全问题处理

### 发现漏洞时的处理流程

1. **查看详细报告**
   ```bash
   pnpm security:full
   ```

2. **自动修复（如果可用）**
   ```bash
   pnpm audit --fix
   ```

3. **手动更新依赖**
   ```bash
   pnpm update [package-name]
   ```

4. **查看具体漏洞信息**
   ```bash
   pnpm audit --json | jq '.advisories'
   ```

### 常见安全问题及解决方案

#### 1. 敏感信息泄露
```javascript
// ❌ 错误示例
const apiKey = "sk-1234567890abcdef";

// ✅ 正确示例
const apiKey = process.env.API_KEY;
```

#### 2. 不安全的函数调用
```javascript
// ❌ 错误示例
eval(userInput);
element.innerHTML = userContent;

// ✅ 正确示例
JSON.parse(userInput);
element.textContent = userContent;
```

#### 3. 动态导入风险
```javascript
// ❌ 错误示例
const module = require(process.env.MODULE_NAME);

// ✅ 正确示例
const allowedModules = ['module1', 'module2'];
if (allowedModules.includes(moduleName)) {
  const module = require(moduleName);
}
```

## 配置自定义

### 修改安全扫描配置

编辑 `scripts/security-check.js` 中的 `SECURITY_CONFIG`：

```javascript
const SECURITY_CONFIG = {
  // 添加新的敏感信息模式
  sensitivePatterns: [
    /your-custom-pattern/gi,
    // ...
  ],
  
  // 修改严重性阈值
  severityLevels: {
    critical: 0,
    high: 0,
    moderate: 10,  // 允许更多中危问题
    low: 20,
  },
};
```

### 修改依赖审计配置

编辑 `scripts/dependency-audit.js` 中的 `AUDIT_CONFIG`：

```javascript
const AUDIT_CONFIG = {
  // 添加豁免的漏洞
  allowedVulnerabilities: [
    'GHSA-xxxx-xxxx-xxxx',
  ],
  
  // 修改过时包阈值
  outdatedThreshold: {
    major: 180,  // 缩短到6个月
    minor: 90,   // 缩短到3个月
    patch: 30,   // 缩短到1个月
  },
};
```

## 最佳实践

### 1. 定期安全检查
- 每日运行：`pnpm security:check`
- 每周运行：`pnpm security:full`
- 发布前运行：完整安全审计

### 2. 依赖管理
- 定期更新依赖：`pnpm update`
- 检查过时包：`pnpm outdated`
- 审查新依赖：添加前先检查安全记录

### 3. 代码审查
- 关注安全相关的ESLint警告
- 审查涉及用户输入的代码
- 检查第三方API集成的安全性

### 4. 环境变量管理
- 使用 `.env.local` 存储敏感配置
- 永远不要提交 `.env` 文件
- 使用 `@t3-oss/env-nextjs` 验证环境变量

## 故障排除

### 常见问题

1. **ESLint安全检查失败**
   ```bash
   # 查看具体错误
   pnpm lint:check
   
   # 自动修复（如果可能）
   pnpm lint:fix
   ```

2. **依赖审计超时**
   ```bash
   # 清理缓存后重试
   pnpm store prune
   pnpm security:deps
   ```

3. **误报处理**
   - 在相应的配置文件中添加忽略规则
   - 或在代码中添加ESLint忽略注释

### 获取帮助

- 查看ESLint安全插件文档：https://github.com/eslint-community/eslint-plugin-security
- 查看pnpm audit文档：https://pnpm.io/cli/audit
- 项目安全问题请联系开发团队

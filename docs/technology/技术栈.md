# 项目技术栈

## 1. 核心框架

### 前端框架

- **Next.js 15.4.1** - React 全栈框架，App Router 架构
- **React 19.1.0** - 用户界面库，支持服务器组件
- **TypeScript 5.8.3** - 类型安全的 JavaScript 超集
- **Tailwind CSS 4.1.11** - 原子化 CSS 框架，CSS-first 配置

### 渲染策略

- **SSG + ISR** - 静态生成 + 增量静态再生，混合渲染模式
- **Server Actions** - 服务器端表单处理和数据变更
- **Edge Runtime（可选）** - 边缘计算支持

## 2. 内容管理

### MDX + next-intl 内容管理系统

- **@next/mdx** - Next.js 原生 MDX 支持，App Router 兼容
- **@mdx-js/loader & @mdx-js/react** - MDX 文件加载和 React 组件嵌入
- **gray-matter** - Frontmatter 解析库，YAML/JSON 元数据支持

### 内容目录结构

```
content/
├── posts/          # 博客文章
│   ├── en/        # 英文内容
│   └── zh/        # 中文内容
├── pages/          # 静态页面
├── documents/      # 文档文件 (PDF ≤20MB)
└── config/         # 全局配置
```

### 核心特性

- **Git-based 工作流** - 版本控制集成，内容变更触发自动部署
- **多语言同步** - 强制同步更新，确保内容一致性
- **类型安全验证** - TypeScript 接口确保数据完整性
- **无数据库架构** - 简化部署流程

### 数据管理服务

- **Airtable 0.12.2** - 云端表格数据库，联系表单数据存储和管理
  - API集成、Web界面、协作功能、数据导出、视图管理、自动化集成

### 通信服务

- **Resend 4.6.0** - 现代邮件发送服务

## 3. UI 设计系统

### 组件库

- **shadcn/ui (New York 风格)** - 现代化UI组件库，RSC 支持
- **@radix-ui/react-\*** - 无障碍组件primitives
- **class-variance-authority (cva)** - 组件样式变体管理
- **clsx + tailwind-merge** - 条件类名和样式合并
- **sonner 2.0.5** - 现代化通知系统
- **embla-carousel-react 8.6.0** - 高性能轮播组件
- **@bprogress/next** - 现代化页面加载进度条，TypeScript 重写版本

### 字体与排版

- **@tailwindcss/typography 0.5.15** - 企业级排版系统
- **next/font + geist 1.4.2** - Vercel官方字体优化
- **苹果中文字体系统** - PingFang SC原生系统字体
- **中英文混排优化** - 智能字体回退策略

### 主题与动画

- **next-themes 0.4.6** - 主题切换（系统/明亮/暗黑）
- **framer-motion 12.23.6** - 统一动画解决方案，替代多库配置 ✅ **架构优化**
- **CSS 变量主题系统** - 动态主题切换

### 表单与验证

- **React Hook Form 7.58.1** - 高性能表单库
- **Zod 3.25.67** - TypeScript 优先的模式验证
- **botid** - Vercel BotID 无感知机器人防护

## 4. 开发工具链

### 代码质量

- **eslint 9.29.0** - 代码质量检查，Flat Config 配置方式
- **typescript-eslint 8.34.1** - TypeScript 专用规则
- **eslint-plugin-react 7.37.5** - React 组件规则
- **eslint-plugin-react-hooks 5.1.0** - React Hooks 最佳实践
- **eslint-plugin-react-you-might-not-need-an-effect 0.4.1** - useEffect 最佳实践检测
- **@next/eslint-plugin-next 15.4.1** - Next.js 专用规则
- **eslint-plugin-import 2.31.0** - 导入语句规则
- **eslint-plugin-promise 7.1.0** - Promise 最佳实践
- **eslint-config-prettier 10.1.5** - Prettier 冲突解决

#### 企业级代码复杂度标准

- **complexity**: 最大复杂度15（警告级别），适合企业级业务逻辑
- **max-depth**: 最大嵌套深度5（错误级别），确保代码可读性
- **max-lines-per-function**: 最大函数行数100（警告级别），React组件适用
- **max-params**: 最大参数数量6（错误级别），适合企业级函数设计
- **特殊文件类型**: 页面组件和API路由有更宽松的限制（200行/复杂度20）

### 代码格式化

- **prettier 3.5.3** - 代码格式化核心
- **@trivago/prettier-plugin-sort-imports 4.3.0** - 导入排序自动化
- **prettier-plugin-tailwindcss 0.6.8** - Tailwind CSS 类名排序

### 构建工具

- **@next/bundle-analyzer 15.4.1** - 包大小分析
- **开发环境构建** - Turbopack (`next dev --turbo`) 极速开发体验
- **生产环境构建** - Webpack 5 + SWC 编译器，稳定可靠的生产构建
- **pnpm 10.9.0** - 现代高性能包管理器，硬链接技术节省空间

#### 构建策略说明

- **开发模式**：使用 Turbopack 获得极速热重载和构建性能
- **生产模式**：使用成熟的 Webpack 5 确保构建稳定性和兼容性
- **编译器**：统一使用 SWC 编译器，提供最佳性能

### Git 工作流

- **lefthook 1.11.14** - Git hooks 管理
- **@commitlint/cli 19.8.1** - 提交信息规范
- **@commitlint/config-conventional 19.8.1** - 约定式提交

### 配置文件清单

- **eslint.config.mjs** - ESLint 9 Flat Config 配置文件
- **.prettierrc.json** - Prettier 代码格式化配置
- **components.json** - shadcn/ui 组件库配置
- **next.config.ts** - Next.js 配置文件（集成 next-intl）
- **tsconfig.json** - TypeScript 配置文件
- **tailwind.config.js** - Tailwind CSS 配置文件
- **.vscode/settings.json** - VS Code 开发环境配置

## 5. 性能优化

### 缓存策略

- **Next.js 内置缓存** - 框架原生缓存机制
- **Git-based 静态生成** - 内容变更触发自动重新构建
- **ISR 配置** - 静态生成 + 增量更新
- **Vercel Edge Cache** - 平台原生 CDN 缓存

### 监控与分析

- **@vercel/analytics 1.5.0** - 性能分析和用户行为追踪
- **web-vitals 5.0.3** - 核心性能指标监控
- **Google Analytics 4** - 详细的用户行为分析
- **错误边界增强** - React Error Boundaries + 基础错误报告

## 6. 安全部署

### 安全防护

- **botid** - Vercel BotID 无感知机器人防护，表单提交和关键交互防护
- **Next.js 安全头配置** - 内置安全头设置 (X-Frame-Options, CSP)
- **Next.js Middleware** - 中间件层面的安全防护

### 环境配置

- **@t3-oss/env-nextjs 0.13.8** - 类型安全的环境变量

### 日志记录与监控

- **Vercel 函数日志** - 服务端监控，API 路由和 Server Actions 性能追踪
- **基础错误日志** - 简单的console.error收集，适合企业官网需求

## 7. 国际化与SEO

### 国际化

- **next-intl 4.3.4** - Next.js 国际化框架 ✅ **已升级** (4.0.0 → 4.3.4)
- **Lingo.dev 0.99.7** - AI 驱动的翻译服务
- **支持语言** - 英语(en) + 中文(zh)

### SEO 优化

- **Next.js 15 Metadata API** - 原生 SEO 优化解决方案
- **统一 metadata 工具函数** - 类型安全的 SEO 配置管理
- **多语言 SEO 支持** - 自动生成 alternates.languages 和本地化配置
- **next-sitemap** - 自动sitemap和hreflang生成
- **静态 OG 图片** - 预设计的社交媒体分享图片
- **结构化数据集成** - Schema.org JSON-LD 支持

### 企业通信

#### WhatsApp 集成方案

**🥇 推荐方案：官方 WhatsApp Business API**

- WhatsApp Business Platform API - Meta官方企业级API
- 2024年11月更新：无限免费服务对话，客户主动发起的对话完全免费
- 企业级功能：官方支持、完整分析报告、多媒体支持、交互式消息、CRM集成

## 8. 可选扩展

### 动画效果 ✅ **架构优化**

- **framer-motion 12.23.6** - 统一动画解决方案 ✅ **已升级** (12.23.0 → 12.23.6)
  - 替代 tailwindcss-animate + react-intersection-observer 多库配置
  - 内置 useInView hook 处理滚动触发动画
  - 统一 API，减少 ~20KB 包体积，降低维护复杂度
  - 支持基础动画、交互动画、页面转场、布局动画
- **intersection-observer (原生API)** - 特殊场景备选（大量元素监听、非动画用途）
- **lucide-react** - 现代图标库
- **lottie-react** - 复杂动画和品牌动画支持

### 媒体处理

- **react-loading-skeleton** - 骨架屏加载状态
- **PDF文档处理** - 推荐使用下载链接方案

### 地图集成（可选）

- **react-leaflet 4.2.1** - 开源地图组件，适合多地点企业

## 9. 项目架构

### 核心目录结构

- **src/app/** - Next.js 15 App Router，国际化路由 `[locale]`
- **src/components/** - 组件库分层：`ui/`、`layout/`、`content/`、`shared/`
- **content/** - MDX 内容存储，多语言目录结构（详见第2章）
- **src/lib/** - 工具库：内容管理、国际化配置、文件处理
- **src/types/** - TypeScript 类型定义
- **messages/** - next-intl 国际化文件

### 设计系统规范

- **主题系统** - 三模式切换（Light/Dark/System），Vercel 风格简约设计
- **组件库** - shadcn/ui New York 风格 + 响应式导航 + 企业级页脚
- **字体系统** - Geist Sans + Geist Mono + 中文字体回退
- **命名规范** - 组件：PascalCase，文件：kebab-case，变量：camelCase

## 测试工具

### 核心测试框架

- **vitest 3.2.4** - 现代单元测试框架
- **@vitest/coverage-v8 3.2.4** - 代码覆盖率分析
- **@playwright/test 1.53.1** - 端到端测试框架
- **@testing-library/react 16.3.0** - React 组件测试
- **@lhci/cli 0.15.0** - Lighthouse CI 自动化性能监控

### WhatsApp 功能测试覆盖

- **API 客户端测试** - WhatsApp API 客户端库完整测试
- **API 路由测试** - 消息发送和 Webhook 处理测试
- **组件测试** - WhatsApp 联系组件和浮动按钮测试
- **E2E 集成测试** - 完整用户流程端到端测试
- **响应式测试** - 移动端、平板、桌面端适配测试
- **无障碍测试** - 键盘导航和 ARIA 标签测试

## 配置最佳实践

### 核心配置验证 ✅

- **中间件配置** - 国际化路由 + 静态资源排除
- **图片优化** - WebP/AVIF 支持 + 缓存策略
- **缓存策略** - SSG/ISR 混合渲染，Git-based 内容更新
- **技术栈兼容性** - Next.js 15 + React 19 + TypeScript 5.8 + Tailwind CSS 4

#### 性能优化建议

- **开发模式**：启用 `next dev --turbo` 获得极速开发体验
- **生产构建**：使用稳定的 Webpack 5 + SWC 编译器组合
- **缓存策略**：合理配置 ISR 和静态生成
- **图片优化**：自动使用 sharp，支持现代格式

#### 安全最佳实践

- **CSP 头部**：配置严格的内容安全策略
- **Server Actions**：使用不可猜测的安全 ID
- **环境变量**：使用 @t3-oss/env-nextjs 验证
- **依赖安全**：定期运行 `pnpm audit` 检查
- **botid 集成**：表单保护和机器人检测

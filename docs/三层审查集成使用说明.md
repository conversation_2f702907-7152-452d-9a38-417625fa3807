# 自动三层审查集成使用说明

## 概述

本集成方案解决了 shrimp-task-manager 的 verify_task 工具不自动触发三层审查的问题。

## 问题背景

- **原始问题**: shrimp-task-manager 的 verify_task 工具只接收预设评分并标记任务完成
- **期望行为**: 任务完成 → 三层审查 → 生成评分 → 验证完成
- **解决方案**: 创建后置钩子，在 verify_task 后自动触发三层审查

## 使用方法

### 1. 自动三层审查验证（推荐）
```bash
pnpm verify:auto-three-layer <taskId> <summary> [score]
```

示例：
```bash
pnpm verify:auto-three-layer "task-123" "任务完成" 95
```

### 2. 仅执行三层审查钩子
```bash
pnpm verify:hook-only <taskId> <summary> <originalScore>
```

示例：
```bash
pnpm verify:hook-only "task-123" "任务完成" 95
```

### 3. 传统三层审查（手动）
```bash
pnpm verify:three-layer
```

## 三层审查流程

### 第一层：自动化检查 (40分)
- TypeScript 类型检查 (15分)
- ESLint 代码规范检查 (15分)  
- Prettier 格式检查 (10分)

### 第二层：AI审查 (35分)
- 基于第一层结果的智能评分
- 代码质量和架构评估

### 第三层：人类确认 (25分)
- 基于前两层结果的综合评估
- 功能验证和用户体验确认

## 评分标准

- **≥90分**: 🏆 优秀！任务质量达到企业级标准
- **≥80分**: ✅ 良好！任务质量符合要求  
- **<80分**: ⚠️ 需要改进！任务质量未达标准

## 日志和报告

- 三层审查日志保存在: `.three-layer-logs/`
- 日志文件格式: `three-layer-{taskId}-{timestamp}.json`

## 跳过三层审查

如需跳过三层审查（不推荐），可以：

1. 设置环境变量: `SKIP_THREE_LAYER=true`
2. 在任务摘要中添加: `[SKIP_THREE_LAYER]`

## 故障排除

如果三层审查失败，系统会：
1. 记录详细错误信息
2. 回退到原始评分
3. 标记为失败但不阻止任务完成
